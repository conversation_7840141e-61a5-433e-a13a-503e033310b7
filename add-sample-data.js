// Script to add sample data to Supabase
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY
// Use service role key for admin operations
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials in .env.local file!')
  process.exit(1)
}

if (!supabaseServiceKey) {
  console.log('Warning: Missing service role key. Some operations may fail.')
}

// Create regular Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

// Create admin Supabase client with service role
const supabaseAdmin = supabaseServiceKey ? 
  createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }) : null;

async function addSampleData() {
  console.log('Adding sample data to Supabase...')
  
  try {
    // Sample data for Twitter stats
    const twitterData = []
    const now = new Date()
    
    // Create 30 days of Twitter data
    for (let i = 0; i < 30; i++) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      
      twitterData.push({
        timestamp: date.toISOString(),
        account_name: 'YourTwitterAccount',
        follower_count: 5000 - Math.floor(Math.random() * 200) + i * 10, // Random growth pattern
        user_id: null // Null for all users to see
      })
    }
    
    // Insert Twitter data
    const { error: twitterError } = await supabase
      .from('twitter_stats')
      .insert(twitterData)
    
    if (twitterError) {
      console.error('Error adding Twitter data:', twitterError.message)
    } else {
      console.log(`✅ Added ${twitterData.length} Twitter stats records`)
    }
    
    // Sample data for Telegram stats
    const telegramData = []
    
    // Create 30 days of Telegram data
    for (let i = 0; i < 30; i++) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      
      telegramData.push({
        timestamp: date.toISOString(),
        group_name: 'YourTelegramGroup',
        member_count: 2500 - Math.floor(Math.random() * 100) + i * 5, // Random growth pattern
        user_id: null // Null for all users to see
      })
    }
    
    // Insert Telegram data
    const { error: telegramError } = await supabase
      .from('telegram_stats')
      .insert(telegramData)
    
    if (telegramError) {
      console.error('Error adding Telegram data:', telegramError.message)
    } else {
      console.log(`✅ Added ${telegramData.length} Telegram stats records`)
    }
    
    // Sample data for PR campaigns
    const prData = [
      {
        outlet: 'TechCrunch',
        pr_type: 'Feature',
        link: 'https://techcrunch.com/example',
        created_at: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: null
      },
      {
        outlet: 'Bloomberg',
        pr_type: 'Interview',
        link: 'https://bloomberg.com/example',
        created_at: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: null
      },
      {
        outlet: 'Forbes',
        pr_type: 'Mention',
        link: 'https://forbes.com/example',
        created_at: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: null
      }
    ]
    
    // Insert PR campaign data (use admin client if available)
    const prClient = supabaseAdmin || supabase;
    const { error: prError } = await prClient
      .from('pr_campaigns')
      .insert(prData)
    
    if (prError) {
      console.error('Error adding PR campaign data:', prError.message)
    } else {
      console.log(`✅ Added ${prData.length} PR campaign records`)
    }
    
    // Sample data for KOLs
    const kolsData = [
      {
        account_handle: '@influencer1',
        tweet_link: 'https://twitter.com/influencer1/status/*********',
        post_date: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: null
      },
      {
        account_handle: '@cryptoexpert',
        tweet_link: 'https://twitter.com/cryptoexpert/status/*********',
        post_date: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: null
      },
      {
        account_handle: '@techreporter',
        tweet_link: 'https://twitter.com/techreporter/status/*********',
        post_date: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: null
      }
    ]
    
    // Insert KOLs data (use admin client if available)
    const kolsClient = supabaseAdmin || supabase;
    const { error: kolsError } = await kolsClient
      .from('kols')
      .insert(kolsData)
    
    if (kolsError) {
      console.error('Error adding KOLs data:', kolsError.message)
    } else {
      console.log(`✅ Added ${kolsData.length} KOLs records`)
    }
    
    console.log('\n✅ Sample data added successfully')
    
  } catch (error) {
    console.error('Unexpected error:', error)
  }
}

// Run the function
addSampleData() 