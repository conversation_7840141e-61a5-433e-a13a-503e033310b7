import { useState, useEffect } from 'react';

// KOL data types
export interface KOLData {
  id: string;
  account_handle: string;
  tweet_link: string;
  post_date: string;
  created_at: string;
  updated_at: string;
  source: 'supabase' | 'sheets';
}

// PR data types
export interface PRData {
  id: string;
  outlet: string;
  pr_type: string;
  link: string;
  created_at: string;
  updated_at: string;
  source: 'supabase' | 'sheets';
}

// Custom hook for fetching KOL data from Google Sheets
export function useKOLSheetData() {
  const [data, setData] = useState<KOLData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/kol-sheet-data');
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        const responseData = await response.json();
        setData(responseData);
      } catch (err) {
        console.error('Error fetching KOL sheet data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

// Custom hook for fetching PR data from Google Sheets
export function usePRSheetData() {
  const [data, setData] = useState<PRData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/pr-sheet-data');
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        const responseData = await response.json();
        setData(responseData);
      } catch (err) {
        console.error('Error fetching PR sheet data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
}

// Helper function to merge KOL data from Supabase and Google Sheets
export function mergeKOLData(supabaseData: any[], sheetData: KOLData[]): KOLData[] {
  // Mark all Supabase data with source
  const markedSupabaseData = supabaseData.map(item => ({
    ...item,
    source: 'supabase' as const
  }));
  
  // Create a map of existing tweet links to avoid duplicates
  const linkMap = new Map();
  markedSupabaseData.forEach(item => {
    if (item.tweet_link) {
      linkMap.set(item.tweet_link, true);
    }
  });
  
  // Filter out sheet data that has duplicate tweet links
  const uniqueSheetData = sheetData.filter(item => {
    if (!item.tweet_link || linkMap.has(item.tweet_link)) {
      return false;
    }
    linkMap.set(item.tweet_link, true);
    return true;
  });
  
  // Combine both data sources
  return [...markedSupabaseData, ...uniqueSheetData];
}

// Helper function to merge PR data from Supabase and Google Sheets
export function mergePRData(supabaseData: any[], sheetData: PRData[]): PRData[] {
  // Mark all Supabase data with source
  const markedSupabaseData = supabaseData.map(item => ({
    ...item,
    source: 'supabase' as const
  }));
  
  // Create a map of existing links to avoid duplicates
  const linkMap = new Map();
  markedSupabaseData.forEach(item => {
    if (item.link) {
      linkMap.set(item.link, true);
    }
  });
  
  // Filter out sheet data that has duplicate links
  const uniqueSheetData = sheetData.filter(item => {
    if (!item.link || linkMap.has(item.link)) {
      return false;
    }
    linkMap.set(item.link, true);
    return true;
  });
  
  // Combine both data sources
  return [...markedSupabaseData, ...uniqueSheetData];
} 