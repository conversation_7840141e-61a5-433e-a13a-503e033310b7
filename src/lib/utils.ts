import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a date string to a more readable format
 * @param dateStr ISO date string
 * @returns Formatted date string (e.g., "Jan 15, 2023")
 */
export function formatDate(dateStr: string): string {
  const date = new Date(dateStr);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  }).format(date);
}

/**
 * Format a date string to a shorter format suitable for charts
 * @param dateStr ISO date string
 * @returns Formatted date string (e.g., "Jan 15")
 */
export function formatChartDate(dateStr: string): string {
  const date = new Date(dateStr);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
  }).format(date);
}

/**
 * Get formatted change information between two values with color indicators
 * @param current Current value
 * @param previous Previous value
 * @returns Object containing text and color for the change
 */
export function getChangeInfo(current: number, previous: number): { text: string; color: string } {
  // If either value is negative, set it to 0
  current = Math.max(0, current);
  previous = Math.max(0, previous);
  
  if (previous === 0 && current === 0) return { text: "No change", color: "text-muted-foreground" };
  if (previous === 0) return { text: `+${current} (new)`, color: "text-green-500" };
  
  const change = current - previous;
  const percentChange = (change / previous) * 100;
  
  // Round to 2 decimal places
  const roundedPercentChange = Math.round(percentChange * 100) / 100;
  
  // Determine color based on change direction
  const color = change >= 0 ? "text-green-500" : "text-red-500";
  
  // Format the change text
  const sign = change >= 0 ? '+' : '';
  const text = `${sign}${change} (${sign}${roundedPercentChange}%)`;
  
  return { text, color };
}

/**
 * Format a number using K, M abbreviations
 * @param value The number to format
 * @returns Formatted string (e.g., 1.2K, 3.4M)
 */
export function formatNumber(value: string | number): string {
  // If it's already a formatted string like "1.2K" or "3.4M", return as is
  if (typeof value === 'string' && /^[\d.,]+\s*[KMB]$/i.test(value.trim())) {
    return value;
  }
  
  // Parse the numeric value
  const num = typeof value === 'string' 
    ? parseFloat(value.replace(/[^0-9.]/g, '')) 
    : value;
  
  // Handle NaN or invalid values
  if (isNaN(num)) return '0';
  
  // Format with abbreviations
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  }
  
  return num.toString();
}
