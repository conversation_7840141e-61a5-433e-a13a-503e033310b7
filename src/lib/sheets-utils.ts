import { google } from 'googleapis';
import { JWT } from 'google-auth-library';

interface HyperlinkResult {
  value: string | null;
  url: string | null;
}

/**
 * Get Google API auth client
 */
export async function getAuthClient() {
  const auth = new JWT({
    email: process.env.GOOGLE_SHEETS_CLIENT_EMAIL,
    key: process.env.GOOGLE_SHEETS_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
  });

  return auth;
}

/**
 * Extracts hyperlinks from a Google Sheet range
 */
export async function extractHyperlinks(
  spreadsheetId: string,
  range: string
): Promise<HyperlinkResult[][]> {
  try {
    // Get auth client
    const authClient = await getAuthClient();
    const sheets = google.sheets({ version: 'v4', auth: authClient });
    
    // First get the cell values
    const valueResponse = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range,
    });
    
    const values = valueResponse.data.values || [];
    
    // Then get the formatting information with hyperlinks
    const formatResponse = await sheets.spreadsheets.get({
      spreadsheetId,
      ranges: [range],
      includeGridData: true,
    });
    
    const result: HyperlinkResult[][] = [];
    
    // Process the sheet data to extract hyperlinks
    const sheetData = formatResponse.data.sheets?.[0].data?.[0];
    
    if (sheetData?.rowData) {
      sheetData.rowData.forEach((row, rowIndex) => {
        const rowLinks: HyperlinkResult[] = [];
        
        if (row.values) {
          row.values.forEach((cell, colIndex) => {
            let url: string | null = null;
            let value: string | null = null;
            
            // Get cell value from values response
            if (values[rowIndex] && values[rowIndex][colIndex] !== undefined) {
              value = values[rowIndex][colIndex];
            }
            
            // Check for direct hyperlink property
            if (cell.hyperlink) {
              url = cell.hyperlink;
            } 
            // Check in rich text format runs
            else if (cell.textFormatRuns) {
              for (const formatRun of cell.textFormatRuns) {
                if (formatRun.format?.link?.uri) {
                  url = formatRun.format.link.uri;
                  break;
                }
              }
            }
            
            rowLinks.push({ value, url });
          });
        }
        
        result.push(rowLinks);
      });
    }
    
    return result;
  } catch (error) {
    console.error('Error extracting hyperlinks:', error);
    throw error;
  }
} 