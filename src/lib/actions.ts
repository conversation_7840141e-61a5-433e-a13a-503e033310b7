'use server';

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { TwitterApi } from 'twitter-api-v2';
import { Telegraf } from 'telegraf';

export async function collectDataAction(authToken: string) {
  // Verify authorization
  const cronSecret = process.env.CRON_SECRET;
  
  if (!cronSecret) {
    throw new Error('Server configuration error: CRON_SECRET not set');
  }
  
  if (authToken !== cronSecret) {
    throw new Error('Unauthorized: Invalid token');
  }
  
  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !serviceRoleKey) {
      throw new Error('Supabase credentials not found in environment variables');
    }
    
    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    console.log('Running data collection directly...');
    
    // Collect Twitter data
    const twitterData = await collectTwitterData(supabase);
    
    // Collect Telegram data
    const telegramData = await collectTelegramData(supabase);
    
    console.log('Data collection completed');
    return { 
      success: true, 
      twitter: twitterData,
      telegram: telegramData
    };
  } catch (error) {
    console.error('Error collecting data:', error);
    throw error;
  }
}

async function collectTwitterData(supabase: SupabaseClient) {
  console.log('Collecting Twitter data...');
  
  const twitterApiKey = process.env.TWITTER_API_KEY;
  const twitterApiSecret = process.env.TWITTER_API_SECRET;
  const twitterAccessToken = process.env.TWITTER_ACCESS_TOKEN;
  const twitterAccessSecret = process.env.TWITTER_ACCESS_SECRET;
  const twitterAccountUsername = process.env.TWITTER_ACCOUNT_USERNAME;
  
  if (!twitterApiKey || !twitterApiSecret || !twitterAccessToken || !twitterAccessSecret || !twitterAccountUsername) {
    throw new Error('Twitter credentials not found in environment variables');
  }
  
  try {
    // Initialize Twitter client
    const twitterClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
      accessToken: twitterAccessToken,
      accessSecret: twitterAccessSecret,
    });
    
    // Get user data
    const user = await twitterClient.v2.userByUsername(twitterAccountUsername, {
      'user.fields': ['public_metrics'],
    });
    
    if (!user.data) {
      throw new Error(`Twitter user '${twitterAccountUsername}' not found`);
    }
    
    const followerCount = user.data.public_metrics?.followers_count || 0;
    const timestamp = new Date().toISOString();
    
    // Store data in Supabase
    const { error } = await supabase
      .from('twitter_stats')
      .insert([{
        account_name: twitterAccountUsername,
        follower_count: followerCount,
        timestamp: timestamp,
      }]);
    
    if (error) throw error;
    
    console.log(`Twitter data collected for @${twitterAccountUsername}: ${followerCount} followers`);
    return { followerCount, timestamp };
  } catch (error) {
    console.error('Error collecting Twitter data:', error);
    throw error;
  }
}

async function collectTelegramData(supabase: SupabaseClient) {
  console.log('Collecting Telegram data...');
  
  const telegramBotToken = process.env.TELEGRAM_BOT_TOKEN;
  const telegramGroupId = process.env.TELEGRAM_GROUP_ID;
  const telegramGroupName = process.env.TELEGRAM_GROUP_NAME || 'MultiBank_io';
  
  if (!telegramBotToken || !telegramGroupId) {
    throw new Error('Telegram credentials not found in environment variables');
  }
  
  try {
    // Initialize Telegram bot
    const bot = new Telegraf(telegramBotToken);
    
    // Get chat info
    const chat = await bot.telegram.getChat(telegramGroupId);
    const memberCount = await bot.telegram.getChatMembersCount(telegramGroupId);
    const chatTitle = 'title' in chat ? chat.title : telegramGroupName;
    const timestamp = new Date().toISOString();
    
    // Store data in Supabase
    const { error } = await supabase
      .from('telegram_stats')
      .insert([{
        group_name: chatTitle,
        member_count: memberCount,
        timestamp: timestamp,
      }]);
    
    if (error) throw error;
    
    console.log(`Telegram data collected for ${chatTitle}: ${memberCount} members`);
    return { memberCount, timestamp };
  } catch (error) {
    console.error('Error collecting Telegram data:', error);
    throw error;
  }
} 