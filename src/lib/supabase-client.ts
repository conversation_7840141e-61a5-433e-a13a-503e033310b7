'use client';

import { createClient } from '@supabase/supabase-js';
import { useAuth } from '@clerk/nextjs';

// Use empty strings as fallbacks to prevent crashes during development
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY || 'placeholder-key';

// Safety check with console warning instead of throwing an error
if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_KEY) {
  console.warn('Missing Supabase environment variables. Check your .env.local file.');
}

// Create a client-side Supabase client
export const supabaseClient = createClient(supabaseUrl, supabaseKey);

// Hook to use Supabase with Clerk auth in client components
export function useSupabaseWithAuth() {
  // Use the full auth object to get more auth state info
  const { userId, isLoaded, isSignedIn } = useAuth();
  
  // Create a Supabase client with custom headers when authenticated
  const getSupabaseClient = () => {
    // Add auth header if user is signed in
    if (isSignedIn && userId) {
      return createClient(supabaseUrl, supabaseKey, {
        global: {
          headers: {
            'X-User-Id': userId
          }
        }
      });
    }
    // Return regular client without auth header
    return supabaseClient;
  };
  
  return {
    supabase: getSupabaseClient(),
    userId,
    isLoaded,
    isSignedIn
  };
}

// Client-side functions

export function useTwitterStats(limit = 30) {
  const { userId, supabase, isLoaded, isSignedIn } = useSupabaseWithAuth();
  
  const fetchTwitterStats = async () => {
    try {
      // Wait until auth is loaded
      if (!isLoaded) {
        console.log('Auth not yet loaded. Waiting for Twitter stats...');
        return [];
      }
      
      // Fetch data with regular client
      if (!isSignedIn) {
        console.log('User not authenticated. Returning empty Twitter stats.');
        return [];
      }
      
      console.log('Fetching all Twitter stats');
      
      // Since we've updated the RLS policies to allow authenticated users to read,
      // we can just use the regular client
      const { data, error } = await supabase
        .from('twitter_stats')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(limit);
      
      if (error) {
        console.error('Error fetching Twitter stats:', error);
        return [];
      }
      
      console.log('Twitter stats fetched successfully:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('Error in useTwitterStats:', error);
      return [];
    }
  };
  
  const fetchLatestTwitterStats = async () => {
    try {
      // Wait until auth is loaded
      if (!isLoaded) {
        console.log('Auth not yet loaded. Waiting for latest Twitter stat...');
        return null;
      }
      
      if (!isSignedIn) {
        console.log('User not authenticated. Returning null for latest Twitter stat.');
        return null;
      }
      
      console.log('Fetching latest Twitter stat');
      
      const { data, error } = await supabase
        .from('twitter_stats')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(1);
      
      if (error) {
        console.error('Error fetching latest Twitter stats:', error);
        return null;
      }
      
      console.log('Latest Twitter stat fetched successfully:', data?.[0] ? 'Found' : 'Not found');
      return data?.[0] || null;
    } catch (error) {
      console.error('Error in fetchLatestTwitterStats:', error);
      return null;
    }
  };
  
  return { fetchTwitterStats, fetchLatestTwitterStats, userId };
}

export function useTelegramStats(limit = 30) {
  const { userId, supabase, isLoaded, isSignedIn } = useSupabaseWithAuth();
  
  const fetchTelegramStats = async () => {
    try {
      // Wait until auth is loaded
      if (!isLoaded) {
        console.log('Auth not yet loaded. Waiting for Telegram stats...');
        return [];
      }
      
      if (!isSignedIn) {
        console.log('User not authenticated. Returning empty Telegram stats.');
        return [];
      }
      
      console.log('Fetching all Telegram stats');
      
      const { data, error } = await supabase
        .from('telegram_stats')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(limit);
      
      if (error) {
        console.error('Error fetching Telegram stats:', error);
        return [];
      }
      
      console.log('Telegram stats fetched successfully:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('Error in useTelegramStats:', error);
      return [];
    }
  };
  
  const fetchLatestTelegramStats = async () => {
    try {
      // Wait until auth is loaded
      if (!isLoaded) {
        console.log('Auth not yet loaded. Waiting for latest Telegram stat...');
        return null;
      }
      
      if (!isSignedIn) {
        console.log('User not authenticated. Returning null for latest Telegram stat.');
        return null;
      }
      
      console.log('Fetching latest Telegram stat');
      
      const { data, error } = await supabase
        .from('telegram_stats')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(1);
      
      if (error) {
        console.error('Error fetching latest Telegram stats:', error);
        return null;
      }
      
      console.log('Latest Telegram stat fetched successfully:', data?.[0] ? 'Found' : 'Not found');
      return data?.[0] || null;
    } catch (error) {
      console.error('Error in fetchLatestTelegramStats:', error);
      return null;
    }
  };
  
  return { fetchTelegramStats, fetchLatestTelegramStats, userId };
}

export function usePRCampaigns() {
  const { userId, supabase, isLoaded, isSignedIn } = useSupabaseWithAuth();
  
  const fetchPRCampaigns = async () => {
    try {
      // Wait until auth is loaded
      if (!isLoaded) {
        console.log('Auth not yet loaded. Waiting...');
        return [];
      }
      
      if (!isSignedIn) {
        console.log('User not authenticated. Returning empty PR campaigns list.');
        return [];
      }
      
      console.log('Fetching all PR campaigns');
      
      try {
        const { data, error } = await supabase
          .from('pr_campaigns')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (error) {
          console.error('Error fetching PR campaigns:', error);
          return [];
        }
        
        console.log('PR campaigns fetched successfully:', data?.length || 0);
        return data || [];
      } catch (dbError) {
        console.error('Database error in fetchPRCampaigns:', dbError);
        return [];
      }
    } catch (error) {
      console.error('Unexpected error in usePRCampaigns:', error);
      return [];
    }
  };
  
  const createPRCampaign = async (campaign: { outlet: string; pr_type: string; link: string }) => {
    try {
      if (!userId) {
        console.log('No authenticated user found. Cannot create PR campaign.');
        return null;
      }
      
      try {
        const { data, error } = await supabase
          .from('pr_campaigns')
          .insert([
            {
              ...campaign,
              user_id: userId,
            },
          ])
          .select();
        
        if (error) {
          console.error('Error creating PR campaign:', error);
          return null;
        }
        
        return data?.[0] || null;
      } catch (dbError) {
        console.error('Database error in createPRCampaign:', dbError);
        return null;
      }
    } catch (error) {
      console.error('Unexpected error in createPRCampaign:', error);
      return null;
    }
  };
  
  return { fetchPRCampaigns, createPRCampaign, userId };
}

export function useKOLs() {
  const { userId, supabase, isLoaded, isSignedIn } = useSupabaseWithAuth();
  
  const fetchKOLs = async () => {
    try {
      // Wait until auth is loaded
      if (!isLoaded) {
        console.log('Auth not yet loaded. Waiting...');
        return [];
      }
      
      if (!isSignedIn) {
        console.log('User not authenticated. Returning empty KOLs list.');
        return [];
      }
      
      console.log('Fetching all KOLs');
      
      try {
        const { data, error } = await supabase
          .from('kols')
          .select('*')
          .order('post_date', { ascending: false });
        
        if (error) {
          console.error('Error fetching KOLs:', error);
          return [];
        }
        
        console.log('KOLs fetched successfully:', data?.length || 0);
        return data || [];
      } catch (dbError) {
        console.error('Database error in fetchKOLs:', dbError);
        return [];
      }
    } catch (error) {
      console.error('Unexpected error in useKOLs:', error);
      return [];
    }
  };
  
  const createKOL = async (kol: { account_handle: string; tweet_link: string; post_date: string }) => {
    try {
      if (!userId) {
        console.log('No authenticated user found. Cannot create KOL.');
        return null;
      }
      
      try {
        const { data, error } = await supabase
          .from('kols')
          .insert([
            {
              ...kol,
              user_id: userId,
            },
          ])
          .select();
        
        if (error) {
          console.error('Error creating KOL:', error);
          return null;
        }
        
        return data?.[0] || null;
      } catch (dbError) {
        console.error('Database error in createKOL:', dbError);
        return null;
      }
    } catch (error) {
      console.error('Unexpected error in createKOL:', error);
      return null;
    }
  };
  
  return { fetchKOLs, createKOL, userId };
}

// Add this to your Database interface
export interface Benchmark {
  id: string;
  platform: 'twitter' | 'telegram';
  target_date: string;
  target_value: number;
  created_at: string;
}

// Add this to your Database interface
export type BenchmarkType = 'twitter' | 'telegram';

// Add this hook for fetching benchmarks
export function useBenchmarks(platform?: BenchmarkType) {
  const { supabase, isLoaded, isSignedIn } = useSupabaseWithAuth();
  
  const fetchBenchmarks = async () => {
    try {
      // Wait until auth is loaded
      if (!isLoaded) {
        console.log('Auth not yet loaded. Waiting for benchmarks...');
        return [];
      }
      
      if (!isSignedIn) {
        console.log('User not authenticated. Returning empty benchmarks list.');
        return [];
      }
      
      console.log(`Fetching benchmarks for ${platform || 'all platforms'}`);
      
      let query = supabase.from('benchmarks').select('*');
      
      if (platform) {
        query = query.eq('platform', platform);
      }
      
      query = query.order('target_date', { ascending: true });
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Error fetching benchmarks:', error);
        return [];
      }
      
      console.log('Benchmarks fetched successfully:', data?.length || 0);
      return data as Benchmark[] || [];
    } catch (error) {
      console.error('Error in useBenchmarks:', error);
      return [];
    }
  };
  
  return { fetchBenchmarks };
} 