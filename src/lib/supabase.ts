import { createClient } from '@supabase/supabase-js';
import { auth } from '@clerk/nextjs/server';

// Use empty strings as fallbacks to prevent crashes during development
// You should still set up proper environment variables in .env.local
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY || 'placeholder-key';

// Safety check with console warning instead of throwing an error
if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_KEY) {
  console.warn('Missing Supabase environment variables. Check your .env.local file.');
}

export const supabase = createClient(supabaseUrl, supabaseKey);

// Create a reusable Supabase client with auth context
export function createSupabaseClient() {
  // Get Supabase credentials from environment
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }
  
  // Create client
  return createClient(supabaseUrl, supabaseKey);
}

// Create a Supabase client with user context (server components)
export async function createSupabaseClientWithAuth() {
  const { userId } = await auth();
  const supabase = createSupabaseClient();
  
  return {
    supabase,
    userId
  };
}

// Functions for Twitter stats

export async function getTwitterStats(limit = 30) {
  try {
    const { userId } = await auth();
    const supabase = createSupabaseClient();
    
    const query = supabase
      .from('twitter_stats')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(limit);
    
    // Add user filter if authenticated
    if (userId) {
      query.or(`user_id.is.null,user_id.eq.${userId}`);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching Twitter stats:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in getTwitterStats:', error);
    return null;
  }
}

export async function getLatestTwitterStats() {
  try {
    const { userId } = await auth();
    const supabase = createSupabaseClient();
    
    const query = supabase
      .from('twitter_stats')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(1);
    
    // Add user filter if authenticated
    if (userId) {
      query.or(`user_id.is.null,user_id.eq.${userId}`);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching latest Twitter stats:', error);
      return null;
    }
    
    return data[0] || null;
  } catch (error) {
    console.error('Error in getLatestTwitterStats:', error);
    return null;
  }
}

// Functions for PR campaigns

export async function getPRCampaigns() {
  try {
    const { userId } = await auth();
    const supabase = createSupabaseClient();
    
    if (!userId) {
      throw new Error('Authentication required');
    }
    
    const { data, error } = await supabase
      .from('pr_campaigns')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching PR campaigns:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in getPRCampaigns:', error);
    return null;
  }
}

export async function createPRCampaign(
  campaign: { outlet: string; pr_type: string; link: string }
) {
  try {
    const { userId } = await auth();
    const supabase = createSupabaseClient();
    
    if (!userId) {
      throw new Error('Authentication required');
    }
    
    const { data, error } = await supabase
      .from('pr_campaigns')
      .insert([
        {
          ...campaign,
          user_id: userId,
        },
      ])
      .select();
    
    if (error) {
      console.error('Error creating PR campaign:', error);
      return null;
    }
    
    return data[0];
  } catch (error) {
    console.error('Error in createPRCampaign:', error);
    return null;
  }
}

// Telegram stats
export async function getTelegramStats(limit: number = 30) {
  try {
    const { userId } = await auth();
    const supabase = createSupabaseClient();
    
    const query = supabase
      .from('telegram_stats')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(limit);
    
    // Add user filter if authenticated
    if (userId) {
      query.or(`user_id.is.null,user_id.eq.${userId}`);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching Telegram stats:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Failed to fetch Telegram stats:', error);
    return [];
  }
}

export async function getLatestTelegramStats() {
  try {
    const { userId } = await auth();
    const supabase = createSupabaseClient();
    
    const query = supabase
      .from('telegram_stats')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(1);
    
    // Add user filter if authenticated
    if (userId) {
      query.or(`user_id.is.null,user_id.eq.${userId}`);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching latest Telegram stats:', error);
      return null;
    }
    
    return data[0] || null;
  } catch (error) {
    console.error('Failed to fetch latest Telegram stats:', error);
    return null;
  }
} 