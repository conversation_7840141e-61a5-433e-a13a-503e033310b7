import { google } from 'googleapis';
import { JWT } from 'google-auth-library';

/**
 * Get Google API auth client for the specified scopes
 */
export async function getAuthClient() {
  // Create a JWT auth client
  const auth = new JWT({
    email: process.env.GOOGLE_SHEETS_CLIENT_EMAIL,
    key: process.env.GOOGLE_SHEETS_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    scopes: [
      'https://www.googleapis.com/auth/spreadsheets.readonly'
    ],
  });

  return auth;
}

/**
 * Extract hyperlinks from a Google Sheet
 */
export async function extractHyperlinks(
  spreadsheetId: string,
  sheetName: string,
  linkColumnNames: string[] = []
): Promise<{ rowLinks: Record<number, Record<string, { value: string; link: string | null }>> }> {
  try {
    // Get auth client
    const authClient = await getAuthClient();
    const sheets = google.sheets({ version: 'v4', auth: authClient });
    
    // Get sheet metadata to find the sheet ID
    const spreadsheet = await sheets.spreadsheets.get({
      spreadsheetId,
    });
    
    console.log(`Looking for sheet "${sheetName}" among:`, 
      spreadsheet.data.sheets?.map(s => `"${s.properties?.title}"`)
    );
    
    // Compare sheet names exactly as strings, without toLowerCase
    let sheet = spreadsheet.data.sheets?.find(
      s => {
        // Trim whitespace from both names before comparing
        const trimmedSheetName = sheetName.trim();
        const trimmedTitle = s.properties?.title?.trim() || '';
        return trimmedTitle === trimmedSheetName;
      }
    );
    
    // If exact match fails, try case-insensitive matching as fallback
    if (!sheet) {
      console.log(`No exact match found for "${sheetName}", trying case-insensitive match...`);
      sheet = spreadsheet.data.sheets?.find(
        s => s.properties?.title?.trim().toLowerCase() === sheetName.trim().toLowerCase()
      );
    }
    
    if (!sheet || !sheet.properties?.sheetId) {
      console.error(`Available sheets:`, spreadsheet.data.sheets?.map(s => s.properties?.title));
      throw new Error(`Sheet "${sheetName}" not found. Available sheets: ${spreadsheet.data.sheets?.map(s => s.properties?.title).join(', ')}`);
    }
    
    // Use the actual title from the sheet object for further operations
    const actualSheetTitle = sheet.properties.title || '';
    console.log(`Using sheet title: "${actualSheetTitle}"`);
    
    // Get all values in the sheet using the actual sheet title
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: actualSheetTitle,
    });
    
    const headers = response.data.values?.[0] || [];
    const rows = response.data.values?.slice(1) || [];
    
    // Get formatting information with hyperlinks using the actual sheet title
    const formatResponse = await sheets.spreadsheets.get({
      spreadsheetId,
      ranges: [actualSheetTitle],
      includeGridData: true,
    });
    
    const sheetData = formatResponse.data.sheets?.[0].data?.[0];
    const rowLinks: Record<number, Record<string, { value: string; link: string | null }>> = {};
    
    // Find column indexes to check for links
    const linkColumnIndexes = linkColumnNames.length > 0 
      ? linkColumnNames.map(name => headers.findIndex((h: string) => h === name)).filter(idx => idx !== -1)
      : headers.map((_: string, idx: number) => idx); // If no specific columns provided, check all columns
    
    // Process each row to extract hyperlinks
    if (sheetData?.rowData) {
      // Skip the header row (index 0)
      for (let rowIndex = 1; rowIndex < sheetData.rowData.length && rowIndex <= rows.length; rowIndex++) {
        const row = sheetData.rowData[rowIndex];
        const dataIndex = rowIndex - 1; // Adjust for header row
        
        rowLinks[dataIndex] = {};
        
        if (row?.values) {
          // Only process columns that we're interested in
          for (const colIndex of linkColumnIndexes) {
            if (colIndex >= row.values.length) continue;
            
            const cell = row.values[colIndex];
            const headerName = headers[colIndex];
            
            // Get cell value from the values response
            const value = rows[dataIndex]?.[colIndex] || '';
            
            // Check for direct hyperlink property
            let url: string | null = null;
            if (cell.hyperlink) {
              url = cell.hyperlink;
            } 
            // Check in rich text format runs
            else if (cell.textFormatRuns) {
              for (const formatRun of cell.textFormatRuns) {
                if (formatRun.format?.link?.uri) {
                  url = formatRun.format.link.uri;
                  break;
                }
              }
            }
            
            // Store link information for this cell
            rowLinks[dataIndex][headerName] = {
              value,
              link: url
            };
          }
        }
      }
    }
    
    return { rowLinks };
  } catch (error) {
    console.error('Error extracting hyperlinks:', error);
    throw error;
  }
} 