"use client"

import { useEffect, useState } from "react"

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)
  
  useEffect(() => {
    if (typeof window !== "undefined") {
      const media = window.matchMedia(query)
      
      // Set initial value
      setMatches(media.matches)
      
      // Create event listener
      const listener = () => setMatches(media.matches)
      
      // Add listener
      if (media.addEventListener) {
        media.addEventListener("change", listener)
        return () => media.removeEventListener("change", listener)
      } else {
        // Fallback for older browsers
        media.addListener(listener)
        return () => media.removeListener(listener)
      }
    }
    
    return undefined
  }, [query])
  
  return matches
} 