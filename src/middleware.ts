import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'

// Define protected routes explicitly
const protectedRoutes = createRouteMatcher([
  '/dashboard',
  '/dashboard/(.*)',
  '/pr',
  '/pr/(.*)',
  '/kols',
  '/kols/(.*)',
  '/settings',
  '/settings/(.*)',
  '/logout'
])

// Define public routes that don't require authentication
const publicRoutes = createRouteMatcher([
  '/sign-in',
  '/sign-in/(.*)',
  '/sign-up',
  '/sign-up/(.*)',
  '/'
])

// Define public API routes
const publicApiRoutes = createRouteMatcher([
  '/api/hello',
  '/api/test',
  '/api/stats',
  '/api/cron/collect-data',
  '/api/cron/health',
  '/api/galxe-data',
  '/api/kol-sheet-data',
  '/api/pr-sheet-data'
])

export default clerkMiddleware(async (auth, request) => {
  if (publicApiRoutes(request)) {
    // Allow these API routes to be accessed without authentication
    return;
  } else if (protectedRoutes(request)) {
    // Redirects unauthenticated users to sign-in
    await auth.protect()
  } else if (!publicRoutes(request)) {
    // This protects all other routes not explicitly listed as public
    await auth.protect()
  }
})

// Configure which routes the middleware applies to
export const config = {
  matcher: [
    // Skip Next.js internals and all static files
    '/((?!_next/image|_next/static|_vercel|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}