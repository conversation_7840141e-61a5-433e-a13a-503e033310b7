import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ReferenceLine,
  ComposedChart,
  Line,
  Legend,
  ReferenceArea,
  Brush
} from "recharts";
import { useTheme } from "next-themes";
import { useMediaQuery } from "@/hooks/use-media-query";
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

export interface TimeSeriesData {
  date: string; // Expect ISO string
  value: number;
  // Add rawDate for reliable tooltip lookup
  rawDate?: string;
  // Add optional growth field
  growth?: number;
}

export interface BenchmarkData {
  target_date: string; // Expect ISO string
  target_value: number;
}

export interface TimeSeriesChartProps {
  title: string;
  data: TimeSeriesData[];
  benchmarks?: BenchmarkData[];
  loading?: boolean;
  valueLabel?: string;
  dateFormat?: (date: string) => string;
  color?: string; // Default color (used for 0% change)
  positiveColor?: string; // Color for positive change
  negativeColor?: string; // Color for negative change
  benchmarkColor?: string; // Optional color for benchmark line
  emptyMessage?: string;
  className?: string;
  useLogScale?: boolean;
  showBrush?: boolean;
  percentChange?: number; // Accept overall percent change as prop
}

export function TimeSeriesChart({
  title,
  data,
  benchmarks = [],
  loading = false,
  valueLabel = "Value",
  dateFormat = (date) => {
      try {
          const d = new Date(date);
          if (isNaN(d.getTime())) return "Invalid Date";
          return format(d, 'MMM dd');
      } catch (e) {
          console.error("Error formatting date:", date, e);
          return "Invalid Date";
      }
  },
  color = "hsl(var(--muted-foreground))",
  positiveColor = "#4ade80",
  negativeColor = "hsl(var(--chart-negative))",
  benchmarkColor = "#ff9800",
  emptyMessage = "No data available",
  className,
  useLogScale = false,
  showBrush = false,
  percentChange = 0,
}: TimeSeriesChartProps) {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === "dark";
  const isMobile = useMediaQuery("(max-width: 640px)");
  const hasData = data && data.length > 0;
  const hasBenchmarks = benchmarks && benchmarks.length > 0;

  const activeColor = useMemo(() => {
    // Use red for losses, green for gains
    return percentChange < 0 ? negativeColor : "#4ade80";
  }, [percentChange, negativeColor]);

  const processedBenchmarks = useMemo(() => {
    if (!hasBenchmarks) return [];
    const sortedBenchmarks = [...benchmarks].sort(
      (a, b) => new Date(a.target_date).getTime() - new Date(b.target_date).getTime()
    );
    const benchmarkPoints: { date: string; targetValue: number }[] = [];
    for (let i = 0; i < sortedBenchmarks.length; i++) {
      const benchmark = sortedBenchmarks[i];
      benchmarkPoints.push({ date: benchmark.target_date, targetValue: benchmark.target_value });
      if (i < sortedBenchmarks.length - 1) {
        const current = new Date(benchmark.target_date);
        const next = new Date(sortedBenchmarks[i + 1].target_date);
        const daysBetween = Math.ceil((next.getTime() - current.getTime()) / (1000 * 60 * 60 * 24));
        if (daysBetween > 7) {
          const valueDiff = sortedBenchmarks[i + 1].target_value - benchmark.target_value;
          const valuePerDay = valueDiff / daysBetween;
          for (let day = 7; day < daysBetween; day += 7) {
            const interpolatedDate = new Date(current);
            interpolatedDate.setDate(current.getDate() + day);
            benchmarkPoints.push({ date: interpolatedDate.toISOString(), targetValue: benchmark.target_value + (valuePerDay * day) });
          }
        }
      }
    }
    benchmarkPoints.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    return benchmarkPoints;
  }, [benchmarks, hasBenchmarks]);

  const combinedData = useMemo(() => {
     if (!hasData && !hasBenchmarks) return [];
    
    // Group data by day, keeping only the latest entry per day
    const dailyDataMap = new Map();
    
    // Process actual data first - keep only latest entry per day
    if (data && data.length > 0) {
      data.forEach(item => {
        if (item.date && item.value !== undefined) {
          const dateStr = typeof item.date === 'string' ? item.date : String(item.date);
          try {
            const dateObj = new Date(dateStr);
            if (isNaN(dateObj.getTime())) throw new Error("Invalid date string");
            
            // Create day-only key (without time) to group by day
            const dayKey = dateObj.toISOString().split('T')[0];
            
            // Check if we already have an entry for this day
            const existingEntry = dailyDataMap.get(dayKey);
            
            // If no entry exists or current timestamp is newer, update the map
            if (!existingEntry || new Date(dateStr) > new Date(existingEntry.rawDate)) {
              const formattedDate = dateFormat(dateStr);
              dailyDataMap.set(dayKey, { 
                date: formattedDate, 
                rawDate: dateStr, 
                value: item.value, 
                growth: item.growth,
                timestamp: dateObj.getTime() // Store timestamp for sorting
              });
            }
          } catch (e) { 
            console.error("Skipping invalid date in data:", dateStr, e); 
          }
        }
      });
    }
    
    // Process benchmarks after actual data
    const dateMap = new Map();
    
    // Convert daily data map to date map with ISO string keys
    dailyDataMap.forEach((value, key) => {
      dateMap.set(value.rawDate, value);
    });
    
    // Add benchmark data
    if (hasBenchmarks) {
      processedBenchmarks.forEach(item => {
        if (item.date && item.targetValue !== undefined) {
          const dateStr = typeof item.date === 'string' ? item.date : String(item.date);
          try {
            const dateObj = new Date(dateStr);
            if (isNaN(dateObj.getTime())) throw new Error("Invalid date string");
            const formattedDate = dateFormat(dateStr);
            
            // Try to find existing entry by exact date
            const existing = dateMap.get(dateStr);
            
            if (existing) {
              // Add benchmark to existing entry
              dateMap.set(dateStr, { ...existing, targetValue: item.targetValue });
            } else {
              // Create daily key for matching
              const dayKey = dateObj.toISOString().split('T')[0];
              
              // Try to find entry by day
              const existingForDay = dailyDataMap.get(dayKey);
              
              if (existingForDay) {
                // Add benchmark to existing day's data
                const updatedEntry = { 
                  ...existingForDay, 
                  targetValue: item.targetValue 
                };
                dateMap.set(existingForDay.rawDate, updatedEntry);
              } else {
                // No matching day entry, create new entry with just benchmark
                dateMap.set(dateStr, { 
                  date: formattedDate, 
                  rawDate: dateStr, 
                  targetValue: item.targetValue 
                });
              }
            }
          } catch (e) { 
            console.error("Skipping invalid date in benchmarks:", dateStr, e); 
          }
        }
      });
    }
    
    // Sort data chronologically
    const sortedData = Array.from(dateMap.values())
      .sort((a, b) => {
        try {
          const dateA = new Date(a.rawDate).getTime();
          const dateB = new Date(b.rawDate).getTime();
          if (isNaN(dateA) || isNaN(dateB)) return 0;
          return dateA - dateB;
        } catch(e) { return 0; }
      });
    
    // Calculate growth between consecutive data points if not already provided
    for (let i = 1; i < sortedData.length; i++) {
      const current = sortedData[i];
      const previous = sortedData[i-1];
      
      if (current.value !== undefined && previous.value !== undefined && current.growth === undefined) {
        // Calculate absolute and percentage growth
        const absoluteGrowth = current.value - previous.value;
        const percentGrowth = previous.value !== 0 ? (absoluteGrowth / previous.value) * 100 : 0;
        
        // Add growth data to current point
        current.absoluteGrowth = absoluteGrowth;
        current.percentGrowth = percentGrowth;
      }
      
      // Calculate benchmark growth if available
      if (current.targetValue !== undefined && previous.targetValue !== undefined) {
        current.benchmarkGrowth = current.targetValue - previous.targetValue;
      }
    }
    
    return sortedData;
  }, [data, processedBenchmarks, hasData, hasBenchmarks, dateFormat]);

  const { minValue, maxValue } = useMemo(() => {
    if (combinedData.length === 0) return { minValue: 0, maxValue: 100 };
    let min = Number.MAX_SAFE_INTEGER;
    let max = 0;
    combinedData.forEach(item => {
        if (item.value !== undefined && item.value !== null) {
             min = Math.min(min, item.value);
             max = Math.max(max, item.value);
        }
        if (item.targetValue !== undefined && item.targetValue !== null) {
             min = Math.min(min, item.targetValue);
             max = Math.max(max, item.targetValue);
        }
    });
    if (min === Number.MAX_SAFE_INTEGER) min = 0;
    min = Math.max(0, Math.floor(min * 0.9));
    max = Math.ceil(max * 1.1);
    if (useLogScale && min < 1) min = 1;
    if (max <= min) max = min + 1;
    return { minValue: min, maxValue: max };
  }, [combinedData, useLogScale]);

  const tooltipStyles = useMemo(() => ({
     background: isDark ? "#222" : "#fff",
     border: `1px solid ${isDark ? "#444" : "#ccc"}`,
     borderRadius: "6px",
     padding: "12px",
     boxShadow: isDark ? "0 4px 12px rgba(0, 0, 0, 0.5)" : "0 4px 12px rgba(0, 0, 0, 0.1)"
  }), [isDark]);

  const formatNumber = (num: number) => {
     if (num >= 1000000) { return `${(num / 1000000).toFixed(1)}M`; }
     if (num >= 1000) { return `${(num / 1000).toFixed(1)}K`; }
     return num.toString();
  };

  const formatXAxisTick = (tickItem: string) => tickItem;

  if (loading) {
    return ( <Card className={`${className || ''} border-2 border-muted overflow-hidden`}><CardHeader className="p-6 pb-0"><CardTitle className="text-base font-medium">{title}</CardTitle></CardHeader><CardContent className="p-6 pt-4"><div className="h-[280px] flex items-center justify-center"><Skeleton className="h-[256px] w-full" /></div></CardContent></Card> );
  }

  const gradientId = `fill-${title.replace(/\s+/g, '-')}-${activeColor.replace(/[^a-zA-Z0-9]/g, '')}`;

  // Custom Tooltip Content Component - Iterate Payload Directly
  const CustomTooltip = ({ active, payload, label }: { active?: boolean; payload?: any[]; label?: string }) => {
    if (active && payload && payload.length) {
      // Get rawDate from the first payload item for accurate date formatting
      const rawDateFromPayload = payload[0]?.payload?.rawDate;
      let displayLabel = label;
      if (rawDateFromPayload) {
          try {
              const date = new Date(rawDateFromPayload);
              if (!isNaN(date.getTime())) { displayLabel = `Date: ${format(date, 'MMM d, yyyy')}`; }
          } catch (e) {}
      } else {
          displayLabel = `Date: ${label}`; // Fallback
      }

      // Get growth information for the tooltip
      const currentPayload = payload[0]?.payload;
      const absoluteGrowth = currentPayload?.absoluteGrowth;
      const percentGrowth = currentPayload?.percentGrowth;
      const benchmarkGrowth = currentPayload?.benchmarkGrowth;
      const hasValue = currentPayload?.value !== undefined;
      const hasBenchmark = currentPayload?.targetValue !== undefined;

      const valueColor = (value: number | undefined) => {
        if (!value) return isDark ? "#fff" : "#333";
        return value > 0 ? positiveColor : value < 0 ? negativeColor : isDark ? "#fff" : "#333";
      };

      return (
        <div style={tooltipStyles}>
          {/* Date section */}
          <p style={{ 
            fontWeight: "bold", 
            marginBottom: "8px", 
            borderBottom: `1px solid ${isDark ? "#444" : "#ddd"}`, 
            paddingBottom: "8px" 
          }}>
            Date: {format(new Date(rawDateFromPayload || label), 'MMM d, yyyy')}
          </p>
          
          {/* Main values section - ALWAYS show both when available */}
          <div style={{
            marginBottom: "5px",
            borderBottom: absoluteGrowth !== undefined ? `1px solid ${isDark ? "#444" : "#ddd"}` : 'none'
          }}>
            {/* Main value (Follower count) */}
            {hasValue && (
              <p style={{ 
                padding: "5px 0",
                fontWeight: "bold", 
                fontSize: "15px",
                color: isDark ? "#fff" : "#333"
              }}>
                {valueLabel}: {currentPayload.value.toLocaleString()}
              </p>
            )}
            
            {/* Benchmark value - Always show when available */}
            {hasBenchmark && (
              <p style={{ 
                paddingBottom: "5px",
                color: isDark ? "#fff" : "#333" 
              }}>
                Benchmark: {currentPayload.targetValue.toLocaleString()}
              </p>
            )}
          </div>
          
          {/* Growth section - only show if growth data exists */}
          {absoluteGrowth !== undefined && (
            <p style={{ 
              paddingTop: "8px",
              color: valueColor(absoluteGrowth),
              fontWeight: "bold"
            }}>
              Growth: {absoluteGrowth > 0 ? "+" : ""}{absoluteGrowth.toLocaleString()}
              {percentGrowth !== undefined && (
                <span style={{ marginLeft: "5px" }}>
                  ({percentGrowth > 0 ? "+" : ""}{percentGrowth.toFixed(2)}%)
                </span>
              )}
            </p>
          )}
        </div>
      );
    }
    return null;
  };


  return (
    <Card className={`${className || ''} overflow-hidden border-opacity-50 w-full`} >
      <CardHeader className="p-4 pb-0">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-2">
        <div className="h-[280px]">
          {combinedData.length === 0 && !loading ? (
            <div className="flex items-center justify-center h-full"><p className="text-muted-foreground text-sm">{emptyMessage}</p></div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart
                data={combinedData}
                margin={{ top: 10, right: isMobile ? 5 : 15, left: isMobile ? 5 : 10, bottom: isMobile ? 50 : 25 }}
                style={{ fontSize: isMobile ? '10px' : '12px' }}
              >
                <defs>
                  <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={activeColor} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={activeColor} stopOpacity={0.2}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" opacity={0.2} stroke={isDark ? "#555555" : "#cccccc"} vertical={true} />
                <ReferenceLine y={0} stroke={isDark ? "#777777" : "#aaaaaa"} strokeWidth={1} />
                <XAxis
                  dataKey="date"
                  angle={-45} textAnchor="end" height={40}
                  tick={{ fontSize: isMobile ? 9 : 11, fill: isDark ? "#ffffff" : "#333333" }}
                  tickLine={{ stroke: isDark ? "#555555" : "#cccccc" }}
                  axisLine={{ stroke: isDark ? "#555555" : "#cccccc" }}
                  stroke={isDark ? "#aaaaaa" : "#666666"} dy={8}
                  tickFormatter={formatXAxisTick} interval="preserveStartEnd" minTickGap={20}
                />
                <YAxis
                  tickLine={{ stroke: isDark ? "#555555" : "#cccccc" }}
                  axisLine={{ stroke: isDark ? "#555555" : "#cccccc" }}
                  stroke={isDark ? "#aaaaaa" : "#666666"} width={isMobile ? 40 : 60}
                  scale={useLogScale ? 'log' : 'auto'}
                  domain={ useLogScale ? [(dataMin: number) => Math.max(1, dataMin * 0.95), (dataMax: number) => dataMax * 1.05] : [(dataMin: number) => Math.max(0, dataMin * 0.95), (dataMax: number) => dataMax * 1.05] }
                  tickFormatter={(value) => formatNumber(value)} tickCount={isMobile ? 4 : 7}
                  tick={{ fontSize: isMobile ? 9 : 11, fill: isDark ? "#ffffff" : "#333333" }}
                  dx={isMobile ? -5 : -10} allowDecimals={false}
                />
                <Tooltip
                  cursor={{ stroke: "#999", strokeDasharray: "5 5" }}
                  isAnimationActive={false}
                  content={<CustomTooltip />}
                  position={{ y: 0, x: 0 }}
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  name={valueLabel} // This name is used by the tooltip formatter
                  stroke={activeColor}
                  strokeWidth={3}
                  activeDot={{ r: 6, stroke: isDark ? "#000" : "#fff", strokeWidth: 2 }}
                  fillOpacity={0.8}
                  fill={`url(#${gradientId})`}
                  connectNulls
                  isAnimationActive={false}
                />
                {/* Always render Benchmark Line when benchmarks exist */}
                {hasBenchmarks && (
                  <Line
                    type="monotone"
                    dataKey="targetValue"
                    name="Benchmark" // This name is used by the tooltip formatter
                    stroke={benchmarkColor}
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={false}
                    activeDot={false}
                    isAnimationActive={false}
                    connectNulls={true}
                  />
                )}
              </ComposedChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
}