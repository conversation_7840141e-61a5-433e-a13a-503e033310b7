"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { PixelCanvas } from "@/components/ui/pixel-canvas"

interface FeatureSectionProps {
  title: string
  description?: string
  colors?: string[]
  icon?: React.ReactNode
}

export function FeatureSection({
  title,
  description,
  colors = ["#3b82f6", "#1d4ed8", "#1e40af"],
  icon
}: FeatureSectionProps) {
  const [isHovered, setIsHovered] = useState(false)
  const pixelId = `feature-${title.toLowerCase().replace(/\s+/g, '-')}`;
  const primaryColor = colors[0];

  return (
    <Card 
      className="group relative overflow-hidden rounded-xl transition-all duration-300 border border-border hover:border-opacity-0 hover:shadow-lg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ "--active-color": primaryColor } as React.CSSProperties}
    >
      <div className="absolute inset-0 z-0">
        <PixelCanvas
          id={pixelId}
          colors={colors.map(c => c + 'CC')}
          speed={25}
          gap={5}
          variant="icon"
          animate={true}
        />
      </div>
      <div className="relative z-10 flex flex-col items-center justify-center h-64 p-8 text-center bg-black/40 dark:bg-black/30 backdrop-blur-sm transition-all duration-300 group-hover:bg-black/10">
        {icon && 
          <div className="mb-4 text-5xl text-white transition-all duration-300 ease-out group-hover:scale-110 group-hover:text-[var(--active-color)]">
            {icon}
          </div>
        }
        <h3 className="text-3xl font-bold mb-4 text-white transition-colors duration-300 group-hover:text-[var(--active-color)]">
          {title}
        </h3>
        {description && (
          <p className="max-w-md text-white/90 group-hover:text-white transition-colors duration-300">
            {description}
          </p>
        )}
      </div>
    </Card>
  )
} 