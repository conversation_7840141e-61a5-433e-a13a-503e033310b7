import Link from "next/link"
import { ModeToggle } from "@/components/mode-toggle"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  BarChart3, 
  Twitter, 
  MessageCircle,
  Menu,
  Settings,
  LogOut
} from "lucide-react"
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON>itle,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet"
import { UserButton } from "@/components/user-button"

export function SiteHeader() {
  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle Menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="pr-0">
              <SheetHeader>
                <SheetTitle>Social Media Tracker</SheetTitle>
                <SheetDescription>
                  Track your social media stats
                </SheetDescription>
              </SheetHeader>
              <nav className="grid gap-6 text-lg font-medium mt-8">
                <Link
                  href="/dashboard"
                  className="flex items-center gap-2 px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <BarChart3 className="h-5 w-5" />
                  Dashboard
                </Link>
                <Link
                  href="#"
                  className="flex items-center gap-2 px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <Twitter className="h-5 w-5" />
                  Twitter Stats
                </Link>
                <Link
                  href="#"
                  className="flex items-center gap-2 px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <MessageCircle className="h-5 w-5" />
                  Telegram Stats
                </Link>
                <Link
                  href="/settings"
                  className="flex items-center gap-2 px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <Settings className="h-5 w-5" />
                  Settings
                </Link>
                <Link
                  href="/logout"
                  className="flex items-center gap-2 px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <LogOut className="h-5 w-5" />
                  Log Out
                </Link>
              </nav>
            </SheetContent>
          </Sheet>
          <Link href="/" className="hidden items-center space-x-2 md:flex">
            <BarChart3 className="h-6 w-6" />
            <span className="font-bold inline-block">Social Media Tracker</span>
          </Link>
        </div>
        <nav className="hidden gap-6 md:flex">
          <Link
            href="/dashboard"
            className="flex items-center text-lg font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Dashboard
          </Link>
          <Link
            href="#"
            className="flex items-center text-lg font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Twitter Stats
          </Link>
          <Link
            href="#"
            className="flex items-center text-lg font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Telegram Stats
          </Link>
          <Link
            href="/settings"
            className="flex items-center text-lg font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Settings
          </Link>
          <Link
            href="/logout"
            className="flex items-center text-lg font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Log Out
          </Link>
        </nav>
        <div className="flex items-center gap-4">
          <ModeToggle />
          <UserButton />
        </div>
      </div>
    </header>
  )
} 