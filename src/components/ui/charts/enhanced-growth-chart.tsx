"use client"

import * as React from 'react';
import { format } from 'date-fns';
import { Area, AreaChart, CartesianGrid, XAxis, YAxis, Tooltip, ResponsiveContainer, ComposedChart, Line } from 'recharts';
import { ArrowUp, ArrowDown, TrendingUp, TrendingDown, Target } from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { cn } from '@/lib/utils';

// Types for data
export interface MetricData {
  date: string; // Expect ISO string or Date object
  value: number;
}

export interface BenchmarkData {
  target_date: string; // Expect ISO string or Date object
  target_value: number;
}

interface EnhancedGrowthChartProps {
  title: string;
  description?: string;
  currentValue: number;
  previousValue: number; // Previous value for the *entire period* for % change calc
  data: MetricData[];
  benchmarks?: BenchmarkData[];
  loading?: boolean;
  className?: string;
  valueLabel?: string;
  color?: string; // Default color (used for 0% overall change line)
  positiveColor?: string; // Color for positive overall change line
  negativeColor?: string; // Color for negative overall change line
  secondaryColor?: string; // For benchmark line
  dateRange?: string;
  showBenchmarksTab?: boolean;
}

export function EnhancedGrowthChart({
  title,
  description,
  currentValue,
  previousValue,
  data = [],
  benchmarks = [],
  loading = false,
  className,
  valueLabel = "Value",
  color = "hsl(var(--muted-foreground))",
  positiveColor = "hsl(var(--chart-positive))",
  negativeColor = "hsl(var(--chart-negative))",
  secondaryColor = "hsl(var(--secondary))",
  dateRange = "Selected period",
  showBenchmarksTab = true,
}: EnhancedGrowthChartProps) {
  const [showBenchmarksView, setShowBenchmarksView] = React.useState(false);

  const percentChange = previousValue
    ? Math.round(((currentValue - previousValue) / previousValue) * 100)
    : 0;

  // Determine active color for the line based on overall trend
  const activeLineColor = React.useMemo(() => {
    if (percentChange > 0) return positiveColor;
    if (percentChange < 0) return negativeColor;
    return color;
  }, [percentChange, positiveColor, negativeColor, color]);

  const displayValue = currentValue.toLocaleString();

  const nextBenchmark = React.useMemo(() => {
    if (!benchmarks || benchmarks.length === 0) return null;
    const now = new Date();
    const futureBenchmarks = benchmarks
      .filter(b => new Date(b.target_date) > now)
      .sort((a, b) => new Date(a.target_date).getTime() - new Date(b.target_date).getTime());
    if (futureBenchmarks.length === 0) return null;
    const next = futureBenchmarks[0];
    const daysUntil = Math.ceil((new Date(next.target_date).getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    const progress = Math.min(100, Math.round((currentValue / next.target_value) * 100));
    return { targetValue: next.target_value, targetDate: next.target_date, daysUntil, progress };
  }, [benchmarks, currentValue]);

  const chartData = React.useMemo(() => {
    if (!data?.length) return [];
    const benchmarkMap = new Map(benchmarks.map(b => [format(new Date(b.target_date), 'yyyy-MM-dd'), b.target_value]));
    return data.map(item => {
      const itemDate = new Date(item.date);
      const formattedDate = format(itemDate, 'MMM dd');
      const lookupDate = format(itemDate, 'yyyy-MM-dd');
      // Ensure rawDate is passed for tooltip formatting
      return { date: formattedDate, rawDate: item.date, value: item.value, target: benchmarkMap.get(lookupDate) };
    });
  }, [data, benchmarks]);

  // Simple gradient ID based on the active line color
  const gradientId = React.useMemo(() => `fill-gradient-${activeLineColor.replace(/[^a-zA-Z0-9]/g, '')}`, [activeLineColor]);

  const chartConfig = React.useMemo(() => {
    return {
      value: { label: valueLabel, color: activeLineColor },
      target: { label: 'Target', color: secondaryColor }
    } satisfies ChartConfig;
  }, [activeLineColor, secondaryColor, valueLabel]);

  const trendingDirection = percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'neutral';
  const trendingText = trendingDirection === 'up'
    ? `Trending up by ${percentChange}% ${dateRange}`
    : trendingDirection === 'down'
      ? `Trending down by ${Math.abs(percentChange)}% ${dateRange}`
      : `No change ${dateRange}`;

  if (loading) {
    return ( <Card className={className}><CardHeader><Skeleton className="h-8 w-3/4" /><Skeleton className="h-4 w-1/2 mt-2" /></CardHeader><CardContent><Skeleton className="h-[220px] w-full" /></CardContent><CardFooter><Skeleton className="h-4 w-2/3" /></CardFooter></Card> );
  }

  const hasBenchmarks = benchmarks && benchmarks.length > 0 && showBenchmarksTab;

  return (
    <Card className={cn(className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div><CardTitle>{title}</CardTitle>{description && <CardDescription>{description}</CardDescription>}</div>
          {hasBenchmarks && (
            <Tabs value={showBenchmarksView ? 'benchmarks' : 'main'} onValueChange={(v) => setShowBenchmarksView(v === 'benchmarks')} className="h-8">
              <TabsList className="h-7 p-0.5">
                <TabsTrigger value="main" className="h-6 px-2 text-xs">{valueLabel}</TabsTrigger>
                <TabsTrigger value="benchmarks" className="h-6 px-2 text-xs">Targets</TabsTrigger>
              </TabsList>
            </Tabs>
          )}
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="text-3xl font-bold">{displayValue}</div>
          {percentChange !== 0 && ( <Badge variant={percentChange > 0 ? "default" : "destructive"} className="text-xs">{percentChange > 0 ? <ArrowUp className="mr-1 h-3.5 w-3.5" /> : <ArrowDown className="mr-1 h-3.5 w-3.5" />}{Math.abs(percentChange)}%</Badge> )}
        </div>
        {nextBenchmark && ( <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground"><Target className="h-3.5 w-3.5" /><span>Next target: {nextBenchmark.targetValue.toLocaleString()} ({nextBenchmark.progress}% complete)</span></div> )}
      </CardHeader>

      <CardContent className="pb-1">
        <div className="h-[220px] w-full">
          <ChartContainer config={chartConfig}>
            <ResponsiveContainer width="100%" height="100%">
              {showBenchmarksView ? (
                <ComposedChart data={chartData} margin={{ top: 5, right: 5, bottom: 5, left: 0 }}>
                   <defs>
                      {/* Simple gradient based on activeLineColor */}
                      <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor={activeLineColor} stopOpacity={0.4} />
                          <stop offset="95%" stopColor={activeLineColor} stopOpacity={0.05} />
                      </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="date" tickLine={false} axisLine={false} tickMargin={8} interval="preserveStartEnd" minTickGap={30} />
                  <YAxis tickLine={false} axisLine={false} tickMargin={8} tickFormatter={(value) => value >= 1000 ? `${(value / 1000).toFixed(0)}k` : value} />
                  {/* Restore Tooltip */}
                  <ChartTooltip cursor={true} content={<ChartTooltipContent indicator="dot" />} />
                  {/* Restore fill */}
                  <Area type="monotone" dataKey="value" fill={`url(#${gradientId})`} stroke={activeLineColor} strokeWidth={2} name={chartConfig.value.label} activeDot={{ r: 6 }} isAnimationActive={false} />
                  <Line type="monotone" dataKey="target" stroke="var(--color-target)" strokeWidth={2} strokeDasharray="5 5" dot={{ r: 4, fill: "var(--color-target)" }} activeDot={{ r: 6, fill: "var(--color-target)" }} name={chartConfig.target.label} connectNulls />
                </ComposedChart>
              ) : (
                <AreaChart data={chartData} margin={{ top: 5, right: 5, bottom: 5, left: 0 }}>
                   <defs>
                       {/* Simple gradient based on activeLineColor */}
                       <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor={activeLineColor} stopOpacity={0.4} />
                          <stop offset="95%" stopColor={activeLineColor} stopOpacity={0.05} />
                      </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="date" tickLine={false} axisLine={false} tickMargin={8} interval="preserveStartEnd" minTickGap={30} />
                  <YAxis tickLine={false} axisLine={false} tickMargin={8} tickFormatter={(value) => value >= 1000 ? `${(value / 1000).toFixed(0)}k` : value} />
                   {/* Restore Tooltip */}
                  <ChartTooltip cursor={true} content={<ChartTooltipContent indicator="dot" />} />
                   {/* Restore fill */}
                  <Area type="monotone" dataKey="value" fill={`url(#${gradientId})`} stroke={activeLineColor} strokeWidth={2} name={chartConfig.value.label} activeDot={{ r: 6 }} isAnimationActive={false} />
                </AreaChart>
              )}
            </ResponsiveContainer>
          </ChartContainer>
        </div>
      </CardContent>

      <CardFooter className="pt-1">
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-1">
            <div className="flex items-center gap-2 font-medium leading-none">{trendingDirection === 'up' ? <TrendingUp className="h-4 w-4 text-emerald-500" /> : trendingDirection === 'down' ? <TrendingDown className="h-4 w-4 text-rose-500" /> : null}{trendingText}</div>
            <div className="text-xs text-muted-foreground">{showBenchmarksView ? 'Comparing actual vs targets' : `Showing ${valueLabel}`}</div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}