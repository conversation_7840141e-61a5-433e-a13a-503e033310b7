"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis, ReferenceLine } from "recharts"
import { format, subDays } from "date-fns"
import { DateRange } from "react-day-picker"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DateRangePicker } from "@/components/date-range-picker"
import { CalendarIcon, Target } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

// Types for data
export interface MetricData {
  date: string;
  value: number;
}

export interface BenchmarkData {
  target_date: string;
  target_value: number;
}

export interface ChartComponentProps {
  title: string;
  description: string;
  data: MetricData[];
  benchmarks?: BenchmarkData[];
  color?: string;
  dataKey?: string;
  className?: string;
  externalTimeRange?: string;
  externalDateRange?: DateRange;
  useExternalDateRange?: boolean;
  hideControls?: boolean;
}

export function ChartComponent({
  title,
  description,
  data,
  benchmarks = [],
  color = "hsl(var(--chart-1))",
  dataKey = "value",
  className,
  externalTimeRange,
  externalDateRange,
  useExternalDateRange = false,
  hideControls = false
}: ChartComponentProps) {
  const [timeRange, setTimeRange] = React.useState("90d")
  const [dateRange, setDateRange] = React.useState<DateRange | undefined>(undefined)
  const [useCustomRange, setUseCustomRange] = React.useState(false)

  // Format data for recharts
  const formattedData = data.map(item => ({
    date: item.date,
    [dataKey]: item.value
  }))

  // Use external or internal controls based on props
  const activeTimeRange = useExternalDateRange ? externalTimeRange || "90d" : timeRange;
  const activeDateRange = useExternalDateRange ? externalDateRange : dateRange;
  const activeUseCustomRange = useExternalDateRange ? !!externalDateRange?.from && !!externalDateRange?.to : useCustomRange;

  const filteredData = React.useMemo(() => {
    if (!formattedData.length) return [];
    
    if (activeUseCustomRange && activeDateRange?.from && activeDateRange?.to) {
      // Use custom date range
      return formattedData.filter(item => {
        const itemDate = new Date(item.date);
        return itemDate >= activeDateRange.from! && itemDate <= activeDateRange.to!;
      });
    } else {
      // Use preset time range
      const referenceDate = new Date(formattedData[formattedData.length - 1].date);
      let daysToSubtract = 90;
      if (activeTimeRange === "30d") {
        daysToSubtract = 30;
      } else if (activeTimeRange === "7d") {
        daysToSubtract = 7;
      }
      
      const startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - daysToSubtract);
      
      return formattedData.filter(item => new Date(item.date) >= startDate);
    }
  }, [formattedData, activeTimeRange, activeDateRange, activeUseCustomRange]);
  
  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    if (range?.from && range?.to) {
      setUseCustomRange(true);
    } else if (range === undefined) {
      setUseCustomRange(false);
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    setUseCustomRange(false);
    setDateRange(undefined);
  };

  // Find the next upcoming benchmark
  const nextBenchmark = React.useMemo(() => {
    if (!benchmarks || benchmarks.length === 0) return null;
    
    const now = new Date();
    
    // Find all future benchmarks and sort by date
    const futureBenchmarks = benchmarks
      .filter(b => new Date(b.target_date) > now)
      .sort((a, b) => 
        new Date(a.target_date).getTime() - new Date(b.target_date).getTime()
      );
    
    if (futureBenchmarks.length === 0) return null;
    
    // Get the next benchmark
    const next = futureBenchmarks[0];
    
    // Calculate days until that benchmark
    const daysUntil = Math.ceil(
      (new Date(next.target_date).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    // Calculate progress as a percentage of the target
    const currentValue = data.length ? data[data.length - 1].value : 0;
    const progress = Math.min(100, Math.round((currentValue / next.target_value) * 100));
    
    return {
      targetValue: next.target_value,
      targetDate: next.target_date, 
      daysUntil,
      progress
    };
  }, [benchmarks, data]);

  // Filter benchmarks to only show those within the selected time range
  const filteredBenchmarks = React.useMemo(() => {
    if (!benchmarks || benchmarks.length === 0 || filteredData.length === 0) return [];
    
    const startDate = new Date(filteredData[0].date);
    const endDate = new Date(filteredData[filteredData.length - 1].date);
    
    return benchmarks.filter(benchmark => {
      const benchmarkDate = new Date(benchmark.target_date);
      return benchmarkDate >= startDate && benchmarkDate <= endDate;
    });
  }, [benchmarks, filteredData]);

  const chartConfig = {
    [dataKey]: {
      label: title,
      color: color,
    },
  } satisfies ChartConfig

  return (
    <Card className={className}>
      <CardHeader className="pb-2 pt-5">
        <div className="space-y-1.5">
          <CardTitle className="text-lg">{title}</CardTitle>
          <CardDescription className="text-sm">
            {description}
          </CardDescription>
          
          {nextBenchmark && (
            <div className="flex items-center gap-1 mt-1.5 text-xs text-muted-foreground">
              <div className="flex items-center gap-1.5">
                <Target className="h-3.5 w-3.5" />
                <span>
                  Next target: {nextBenchmark.targetValue.toLocaleString()} ({nextBenchmark.progress}% complete, {nextBenchmark.daysUntil}d left)
                </span>
              </div>
            </div>
          )}
        </div>
        {!hideControls && (
          <div className="flex gap-2 mt-3">
            <DateRangePicker
              dateRange={dateRange}
              onDateRangeChange={handleDateRangeChange}
            />
            
            <Select 
              value={useCustomRange ? '' : timeRange} 
              onValueChange={handleTimeRangeChange}
              disabled={useCustomRange}
            >
              <SelectTrigger
                className="w-[120px] rounded-lg"
                aria-label="Select time range"
              >
                <SelectValue placeholder="Time range" />
              </SelectTrigger>
              <SelectContent className="rounded-xl">
                <SelectItem value="90d" className="rounded-lg">
                  Last 3 months
                </SelectItem>
                <SelectItem value="30d" className="rounded-lg">
                  Last 30 days
                </SelectItem>
                <SelectItem value="7d" className="rounded-lg">
                  Last 7 days
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </CardHeader>
      <CardContent className="p-0">
        <div className="w-full h-[280px]">
          <ChartContainer config={chartConfig}>
            <AreaChart 
              data={filteredData}
              margin={{ top: 5, right: 5, bottom: 5, left: 5 }}
            >
              <defs>
                <linearGradient id={`fill-${dataKey}-${title.replace(/\s+/g, '')}`} x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor={color}
                    stopOpacity={0.6}
                  />
                  <stop
                    offset="95%"
                    stopColor={color}
                    stopOpacity={0.1}
                  />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} opacity={0.2} />
              <XAxis
                dataKey="date"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                minTickGap={32}
                tickFormatter={(value) => {
                  const date = new Date(value)
                  return date.toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                  })
                }}
              />
              <ChartTooltip
                cursor={false}
                content={
                  <ChartTooltipContent
                    indicator="dot"
                  />
                }
              />
              <Area
                dataKey={dataKey}
                type="monotone"
                fill={`url(#fill-${dataKey}-${title.replace(/\s+/g, '')})`}
                stroke={color}
                strokeWidth={2}
                activeDot={{ r: 8 }}
              />
              
              {/* Render benchmark reference lines */}
              {filteredBenchmarks.map((benchmark, index) => (
                <ReferenceLine
                  key={index}
                  x={benchmark.target_date}
                  stroke={color}
                  strokeOpacity={0.5}
                  strokeDasharray="3 3"
                  label={{ 
                    value: `Target: ${benchmark.target_value.toLocaleString()}`,
                    position: 'insideTopRight',
                    fill: color,
                    fontSize: 10,
                    opacity: 0.8
                  }}
                />
              ))}
            </AreaChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  )
} 