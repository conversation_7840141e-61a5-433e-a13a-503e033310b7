'use client';

import * as React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { <PERSON>rkles, ArrowUp, ArrowDown, Target, LineChart } from 'lucide-react';

// Types for data
export interface MetricData {
  date: string;
  value: number;
}

export interface BenchmarkData {
  target_date: string;
  target_value: number;
}

interface GrowthChartProps {
  title: string;
  description?: string;
  currentValue: number;
  previousValue: number;
  data: MetricData[];
  benchmarks?: BenchmarkData[];
  loading?: boolean;
  className?: string;
  valueLabel?: string;
  useRelativeValues?: boolean;
  baselineValue?: number;
  color?: string;
  icon?: React.ReactNode;
}

export function GrowthChart({
  title,
  description,
  currentValue,
  previousValue,
  data = [],
  benchmarks = [],
  loading = false,
  className,
  valueLabel = "Value",
  useRelativeValues = false,
  baselineValue = 0,
  color = "hsl(var(--primary))",
  icon
}: GrowthChartProps) {
  const [viewMode, setViewMode] = React.useState<'all' | 'trend'>('all');
  
  // Calculate percent change
  const percentChange = previousValue 
    ? Math.round(((currentValue - previousValue) / previousValue) * 100) 
    : 0;
  
  // Format values for display
  const displayValue = useRelativeValues 
    ? Math.max(0, currentValue - baselineValue).toLocaleString()
    : currentValue.toLocaleString();
  
  // Find the next upcoming benchmark
  const nextBenchmark = React.useMemo(() => {
    if (!benchmarks || benchmarks.length === 0) return null;
    
    const now = new Date();
    
    // Find all future benchmarks and sort by date
    const futureBenchmarks = benchmarks
      .filter(b => new Date(b.target_date) > now)
      .sort((a, b) => 
        new Date(a.target_date).getTime() - new Date(b.target_date).getTime()
      );
    
    if (futureBenchmarks.length === 0) return null;
    
    // Get the next benchmark
    const next = futureBenchmarks[0];
    
    // Calculate days until that benchmark
    const daysUntil = Math.ceil(
      (new Date(next.target_date).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    // Calculate progress as a percentage of the target
    const displayedValue = useRelativeValues 
      ? Math.max(0, currentValue - baselineValue)
      : currentValue;
    
    const progress = Math.min(100, Math.round((displayedValue / next.target_value) * 100));
    
    return {
      targetValue: next.target_value,
      targetDate: next.target_date, 
      daysUntil,
      progress
    };
  }, [benchmarks, currentValue, useRelativeValues, baselineValue]);
  
  // Generate sparkline points 
  const sparklinePoints = React.useMemo(() => {
    if (data.length < 2) return '';
    
    // Take the most recent values (up to 30 points) for the sparkline
    const points = data.slice(-30).map(item => {
      const value = useRelativeValues 
        ? Math.max(0, item.value - baselineValue)
        : item.value;
      return value;
    });
    
    // Find min and max to normalize values to 0-100 range
    const min = Math.min(...points);
    const max = Math.max(...points);
    const range = max - min || 1; // Avoid division by zero
    
    // Normalize points to 0-40 range (keeping the sparkline in lower half of the card)
    const normalizedPoints = points.map(p => 40 - Math.round(((p - min) / range) * 40));
    
    // Generate SVG path from points
    const width = 100 / (normalizedPoints.length - 1);
    
    return normalizedPoints.map((point, i) => {
      return `${i * width},${point}`;
    }).join(' ');
  }, [data, useRelativeValues, baselineValue]);
  
  // Change badge coloring
  const trendBadge = React.useMemo(() => {
    if (percentChange > 0) {
      return (
        <Badge variant="outline" className="bg-emerald-500/10 text-emerald-600 dark:text-emerald-400 border-emerald-500/20">
          <ArrowUp className="mr-1 h-3 w-3" />
          {percentChange}%
        </Badge>
      );
    } else if (percentChange < 0) {
      return (
        <Badge variant="outline" className="bg-rose-500/10 text-rose-600 dark:text-rose-400 border-rose-500/20">
          <ArrowDown className="mr-1 h-3 w-3" />
          {Math.abs(percentChange)}%
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="bg-muted text-muted-foreground">
          0%
        </Badge>
      );
    }
  }, [percentChange]);
  
  // Add loading state implementation
  if (loading) {
    return (
      <Card className={cn("overflow-hidden transition-all duration-200", className)}>
        <CardHeader className="p-4 pb-0">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-3">
            <div className="h-7 w-28 bg-muted rounded animate-pulse" />
            <div className="h-4 w-16 bg-muted rounded animate-pulse" />
            <div className="h-20 w-full bg-muted/50 rounded-md mt-4 animate-pulse" />
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className={cn("overflow-hidden transition-all duration-200", className)}>
      <CardHeader className="p-4 pb-0 flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </div>
        <Tabs 
          value={viewMode} 
          onValueChange={(v) => setViewMode(v as 'all' | 'trend')}
          className="h-8"
        >
          <TabsList className="h-7 p-0.5">
            <TabsTrigger value="all" className="h-6 px-2 text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="trend" className="h-6 px-2 text-xs">
              Trend
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="p-0">
        <div className="p-4 pt-0">
          <div className="flex items-center justify-between mt-2">
            <div className="space-y-1">
              <div className="text-2xl font-bold">{displayValue}</div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {valueLabel}
                {trendBadge}
              </div>
            </div>
            {nextBenchmark && (
              <div className="flex flex-col items-end">
                <div className="text-xs flex items-center gap-1">
                  <Target className="h-3 w-3 text-primary" />
                  <span className="text-muted-foreground">
                    {nextBenchmark.targetValue.toLocaleString()} in {nextBenchmark.daysUntil}d
                  </span>
                </div>
                <div className="w-24 h-1.5 bg-muted rounded-full mt-1 overflow-hidden">
                  <div 
                    className="h-full bg-primary" 
                    style={{ width: `${nextBenchmark.progress}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Sparkline visualization */}
        <div className="h-24 w-full overflow-hidden relative">
          {/* Gradient background */}
          <div 
            className="absolute inset-0 bg-gradient-to-t from-transparent"
            style={{ 
              color,
              backgroundImage: `linear-gradient(to top, ${color}10, transparent)` 
            }}
          />
          
          {/* SVG sparkline */}
          {sparklinePoints && (
            <svg
              className="absolute inset-0 overflow-visible"
              preserveAspectRatio="none"
              viewBox="0 0 100 40"
              fill="none"
              stroke={color}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <defs>
                <linearGradient id={`sparkline-gradient-${title.replace(/\s+/g, '')}`} x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor={color} stopOpacity="0.3" />
                  <stop offset="100%" stopColor={color} stopOpacity="0" />
                </linearGradient>
              </defs>
              
              {/* Line */}
              <polyline points={sparklinePoints} />
              
              {/* Filled area under the line */}
              <polyline 
                points={`0,40 ${sparklinePoints} 100,40`} 
                fill={`url(#sparkline-gradient-${title.replace(/\s+/g, '')})`} 
                stroke="none" 
              />
            </svg>
          )}
          
          {/* Benchmark indicators */}
          {viewMode === 'all' && benchmarks && benchmarks.length > 0 && (
            <div className="absolute inset-0 flex items-end justify-between px-6">
              {benchmarks.slice(0, 3).map((benchmark, i) => {
                const date = new Date(benchmark.target_date);
                return (
                  <div 
                    key={i} 
                    className="flex flex-col items-center"
                    style={{ 
                      opacity: 0.7 + (i * 0.1),
                    }}
                  >
                    <div className="w-px h-3 bg-primary mb-1" />
                    <div className="text-[10px] font-medium text-primary">
                      {date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 