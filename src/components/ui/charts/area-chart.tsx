"use client"

import { <PERSON><PERSON><PERSON>Up, <PERSON>U<PERSON> } from "lucide-react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"
import { format } from 'date-fns'
import * as React from 'react'

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Types for data
export interface MetricData {
  date: string;
  value: number;
}

export interface AreaChartProps {
  title: string;
  description: string;
  data: MetricData[];
  currentValue?: number;
  percentChange?: number;
  dateRange?: string;
  color?: string;
  valueLabel?: string;
  className?: string;
}

export function AreaChartComponent({
  title, 
  description, 
  data,
  currentValue,
  percentChange = 5.2,
  dateRange = "Last 1d",
  color = "#1DA1F2",
  valueLabel = "Followers",
  className
}: AreaChartProps) {
  const [activeTab, setActiveTab] = React.useState<'all' | 'trend'>('all');
  
  // Format data for the chart
  const chartData = data.map(item => ({
    date: typeof item.date === 'string' ? format(new Date(item.date), 'MMM dd') : item.date,
    value: item.value
  }))

  const chartConfig = {
    value: {
      label: valueLabel,
      color,
    },
  } satisfies ChartConfig

  // Target value (could come from props)
  const targetValue = data.length ? data[data.length - 1].value + 5000 : 0
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <div className="flex flex-col">
          <CardTitle>{title}</CardTitle>
          <CardDescription className="text-sm">
            {description}
          </CardDescription>
        </div>
        <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'all' | 'trend')} className="h-8">
          <TabsList className="h-7 p-0.5">
            <TabsTrigger value="all" className="h-6 px-4 text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="trend" className="h-6 px-4 text-xs">
              Trend
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      
      <div className="px-6">
        <div className="text-3xl font-bold">
          {currentValue?.toLocaleString() || (data.length ? data[data.length - 1].value.toLocaleString() : '0')}
        </div>
        <div className="flex items-center gap-2 mt-1">
          <span className="text-sm text-muted-foreground">{valueLabel}</span>
          <Badge variant={percentChange >= 0 ? "default" : "destructive"} className="text-xs">
            <ArrowUp className="h-3.5 w-3.5 mr-1" />
            {Math.abs(percentChange)}%
          </Badge>
          
          <div className="flex items-center gap-1 ml-auto text-sm text-muted-foreground">
            <span className="inline-block h-2 w-2 rounded-full bg-muted"></span>
            {targetValue.toLocaleString()} in 1d
          </div>
        </div>
      </div>
      
      <CardContent className="p-0 mt-4">
        <ChartContainer config={chartConfig}>
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              top: 0,
              right: 0,
              bottom: 0,
              left: 0,
            }}
            height={160}
          >
            <defs>
              <linearGradient id={`color-${title.replace(/\s+/g, '')}`} x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={color} stopOpacity={0.4} />
                <stop offset="100%" stopColor={color} stopOpacity={0.0} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              padding={{ left: 20, right: 20 }}
              style={{ fontSize: '12px' }}
            />
            <Area
              type="monotone"
              dataKey="value"
              stroke={color}
              strokeWidth={2}
              fill={`url(#color-${title.replace(/\s+/g, '')})`}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 