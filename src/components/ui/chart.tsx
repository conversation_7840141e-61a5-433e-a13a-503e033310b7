"use client"

import * as React from "react"
import { Tooltip as RechartsTooltip, TooltipProps } from "recharts"
import { format } from "date-fns" // Moved import to top
import { cn } from "@/lib/utils"

type ChartConfig = Record<
  string,
  {
    label: string
    color: string
  }
>

interface ChartTooltipContentProps
  extends Pick<TooltipProps<number, string>, "active" | "payload" | "label"> {
  indicator?: "dot" | "line"
}

const ChartContainer = ({
  config,
  children,
  className,
}: {
  config: ChartConfig
  children: React.ReactNode
  className?: string
}) => {
  const variables = React.useMemo(() => {
    return Object.entries(config).reduce(
      (acc, [key, value]) => {
        acc[`--color-${key}`] = value.color
        return acc
      },
      {} as Record<string, string>
    )
  }, [config])

  return (
    <div
      style={variables}
      className={cn("h-full w-full text-xs font-medium text-muted-foreground", className)}
    >
      {children}
    </div>
  )
}

const ChartTooltip = React.forwardRef<
  React.ElementRef<typeof RechartsTooltip>,
  React.ComponentPropsWithoutRef<typeof RechartsTooltip>
>(({ content, ...props }, ref) => (
  <RechartsTooltip {...props} content={content} ref={ref} />
))

ChartTooltip.displayName = "ChartTooltip"

const ChartTooltipContent = ({
  active,
  payload,
  label,
  indicator = "dot",
}: ChartTooltipContentProps) => {
  if (!active || !payload?.length) {
    return null
  }

  const rawPayloadData = payload[0]?.payload;
  const rawDate = rawPayloadData?.rawDate;
  let displayLabel = label;

  if (rawDate) {
    try {
      const dateObj = new Date(rawDate);
      if (!isNaN(dateObj.getTime())) {
         displayLabel = format(dateObj, "MMM d, yyyy");
      } else {
        console.warn("Invalid rawDate found in tooltip payload:", rawDate);
      }
    } catch (e) {
      console.error("Failed to format rawDate for tooltip:", e);
      displayLabel = label;
    }
  }

  return (
    <div className="rounded-lg border bg-background p-2 shadow-sm">
      <div className="grid gap-2">
        <p className="text-xs font-medium text-foreground">{displayLabel}</p>
        <div className="grid gap-1">
          {payload.map((data, i) => (
            <div key={i} className="flex items-center gap-2">
              {indicator === "dot" && (
                <div
                  className="h-1.5 w-1.5 rounded-full"
                  style={{ backgroundColor: data.color }}
                />
              )}
              {indicator === "line" && (
                <div
                  className="h-2.5 w-0.5 rounded-full"
                  style={{ backgroundColor: data.color }}
                />
              )}
              <span className="text-xs font-bold leading-none text-foreground">
                {data.name}
              </span>
              <span className="ml-auto text-right text-xs font-medium leading-none text-foreground">
                {typeof data.value === 'number' ? data.value.toLocaleString() : data.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig }