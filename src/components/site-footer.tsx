import { Gith<PERSON> } from "lucide-react"
import { Separator } from "@/components/ui/separator"

export function SiteFooter() {
  return (
    <footer className="border-t py-8">
      <div className="container max-w-7xl mx-auto px-8 flex flex-col items-center justify-between gap-8 md:flex-row">
        <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
          Built with Next.js, Shadcn/UI, and Supabase
        </p>
        <div className="flex items-center gap-8">
          <a 
            href="https://github.com" 
            target="_blank"
            rel="noreferrer"
            className="flex items-center gap-2 text-sm text-muted-foreground transition-colors hover:text-foreground"
          >
            <Github className="h-4 w-4" />
            <span>GitHub</span>
          </a>
          <Separator orientation="vertical" className="h-4" />
          <a 
            href="#"
            className="text-sm text-muted-foreground transition-colors hover:text-foreground"
          >
            Documentation
          </a>
        </div>
      </div>
    </footer>
  )
} 