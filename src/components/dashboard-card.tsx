"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { PixelCanvas } from "@/components/ui/pixel-canvas"
import { useMediaQuery } from "@/hooks/use-media-query"

interface DashboardCardProps {
  title: string
  value: string | number
  description?: string
  descriptionColor?: string
  icon?: React.ReactNode
  colors?: string[]
  loading?: boolean
  className?: string
}

export function DashboardCard({
  title,
  value,
  description,
  descriptionColor,
  icon,
  colors = ["#ec4899", "#db2777", "#be185d"],
  loading = false,
  className,
}: DashboardCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const isMobile = useMediaQuery("(max-width: 640px)")
  const pixelId = `dashboard-${title.toLowerCase().replace(/\s+/g, '-')}`;
  const primaryColor = colors[0];

  // For mobile devices, use touch events instead of hover
  const handleTouchStart = () => {
    if (isMobile) {
      setIsHovered(true);
    }
  };

  const handleTouchEnd = () => {
    if (isMobile) {
      setIsHovered(false);
    }
  };

  return (
    <Card 
      className={cn(
        "group relative overflow-hidden transition-all duration-300 border border-border hover:border-opacity-0 hover:shadow-lg",
        className
      )}
      onMouseEnter={() => !isMobile && setIsHovered(true)}
      onMouseLeave={() => !isMobile && setIsHovered(false)}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      style={{ "--active-color": primaryColor } as React.CSSProperties}
    >
      <div className="absolute inset-0 z-0">
        <PixelCanvas 
          id={pixelId}
          colors={colors.map(c => c + 'CC')}
          speed={25}
          gap={isMobile ? 3 : 5}
          variant="default"
          animate={isHovered}
        />
      </div>
      <div className="relative z-10 p-4 sm:p-6 bg-black/40 dark:bg-black/30 backdrop-blur-sm transition-all duration-300 group-hover:bg-black/10 h-full">
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h3 className="text-xs sm:text-sm font-medium text-white transition-colors duration-300 group-hover:text-[var(--active-color)]">
            {title}
          </h3>
          {icon && (
            <span className="text-white transition-all duration-300 ease-out group-hover:scale-110 group-hover:text-[var(--active-color)]">
              {icon}
            </span>
          )}
        </div>
        {loading ? (
          <Skeleton className="h-6 sm:h-8 w-[100px] sm:w-[120px]" />
        ) : (
          <div className="text-xl sm:text-3xl font-bold text-white transition-all duration-500 ease-out group-hover:scale-105 group-hover:text-[var(--active-color)]">
            {value}
          </div>
        )}
        {description && (
          <p className={cn("text-[10px] sm:text-xs mt-2 sm:mt-4 transition-colors duration-300 group-hover:text-white", 
            descriptionColor || "text-white/90")}>
            {description}
          </p>
        )}
      </div>
    </Card>
  )
} 