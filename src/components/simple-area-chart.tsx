"use client"

import * as React from "react"
import { TrendingUp } from "lucide-react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"
import { format } from 'date-fns';

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

// Define the expected data structure
interface ChartDataPoint {
  date: string; // Expect ISO string or Date object
  value: number;
  rawDate?: string;
}

interface SimpleAreaChartProps {
  title: string;
  description?: string;
  data: ChartDataPoint[];
  dataKey?: string;
  color?: string;
  valueLabel?: string;
  footerText?: string;
  className?: string;
  loading?: boolean;
}

export function SimpleAreaChart({
  title,
  description,
  data = [],
  dataKey = 'value',
  color = "hsl(var(--chart-1))",
  valueLabel = "Value",
  footerText,
  className,
  loading = false,
}: SimpleAreaChartProps) {

  const chartData = React.useMemo(() => {
    // Add safety check for date parsing
    return data.map(item => {
      let formattedDate = "Invalid Date";
      let rawDateStr = item.date;
      try {
        const dateObj = new Date(item.date);
        if (!isNaN(dateObj.getTime())) {
          formattedDate = format(dateObj, 'MMM dd');
        } else {
           console.warn("Invalid date encountered in chart data:", item.date);
           rawDateStr = "Invalid Date"; // Prevent passing invalid date string
        }
      } catch (e) {
         console.error("Error parsing date:", item.date, e);
         rawDateStr = "Invalid Date";
      }
      return {
        ...item,
        date: formattedDate,
        rawDate: rawDateStr
      };
    });
  }, [data]);
 
  const chartConfig = React.useMemo(() => ({
    // Use hardcoded 'value' key for debugging, matching the default dataKey
    value: {
      label: valueLabel,
      color: color,
    },
  }), [dataKey, valueLabel, color]) satisfies ChartConfig;

  if (loading) {
     return ( <Card className={className}><CardHeader><CardTitle>{title}</CardTitle>{description && <CardDescription>{description}</CardDescription>}</CardHeader><CardContent><Skeleton className="h-[180px] w-full" /></CardContent>{footerText && <CardFooter><div className="text-sm text-muted-foreground">{footerText}</div></CardFooter>}</Card> );
  }

  // Handle case where data might be empty after filtering invalid dates
  if (chartData.length === 0 && !loading) {
     return ( <Card className={className}><CardHeader><CardTitle>{title}</CardTitle>{description && <CardDescription>{description}</CardDescription>}</CardHeader><CardContent><div className="h-[180px] w-full flex items-center justify-center text-muted-foreground">No valid data available</div></CardContent>{footerText && <CardFooter><div className="text-sm text-muted-foreground">{footerText}</div></CardFooter>}</Card> );
  }


  return (
    <Card className={cn(className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{ left: 0, right: 5, top: 5, bottom: 0 }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              interval="preserveStartEnd"
              minTickGap={20}
            />
            <ChartTooltip
              cursor={true}
              content={<ChartTooltipContent indicator="dot" />}
            />
            {/* Removed <defs> and fill prop */}
            <Area
              dataKey={dataKey}
              type="natural"
              // fill={`url(#fill-${dataKey})`} // Removed fill
              // fillOpacity={1} // Removed fillOpacity
              stroke={color} // Ensure stroke is present
              strokeWidth={2}
              isAnimationActive={false}
              activeDot={{ r: 6 }}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
      {footerText && (
        <CardFooter>
          <div className="flex w-full items-start gap-2 text-sm">
            <div className="grid gap-2">
              <div className="leading-none text-muted-foreground">
                {footerText}
              </div>
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  )
}