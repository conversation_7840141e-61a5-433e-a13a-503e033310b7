'use client';

import { UserProfile } from '@clerk/nextjs';
import Link from 'next/link';
import { ArrowLeft, Settings } from 'lucide-react';
import { PixelCanvas } from '@/components/ui/pixel-canvas';

export default function SettingsPage() {
  return (
    <div className="pt-6 pb-12">
      <div className="bg-muted/40 py-8 border-b relative overflow-hidden">
        <div className="absolute inset-0 z-0 opacity-30">
          <PixelCanvas 
            id="settings-header-canvas"
            colors={['#0ea5e9CC', '#0284c7CC', '#0369a1CC']} // Blue colors
            speed={30}
            gap={5}
            variant="default"
            animate={true}
          />
        </div>
        <div className="container max-w-7xl mx-auto px-8 relative z-10">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <Link href="/dashboard" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-4 bg-background/50 backdrop-blur-sm px-3 py-1 rounded-md hover:bg-background/70 transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold tracking-tight mb-2">Account Settings</h1>
              <p className="text-muted-foreground backdrop-blur-sm bg-background/30 px-3 py-1 rounded-md inline-block">
                Manage your profile and account preferences
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container max-w-4xl mx-auto px-8 py-8">
        <UserProfile 
          appearance={{
            elements: {
              rootBox: "mx-auto w-full",
              card: "border rounded-lg shadow-sm p-8 bg-background",
              navbar: "mb-8",
              navbarButton: "font-medium",
              headerTitle: "text-xl font-bold",
              headerSubtitle: "text-muted-foreground",
            }
          }}
        />
      </div>
    </div>
  );
} 