import { createClient } from '@supabase/supabase-js' // SupabaseClient removed
import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { subDays, parseISO, isWithinInterval, isValid, startOfDay, endOfDay } from 'date-fns' // Added date-fns utils
import { rawBenchmarkData, RawBenchmarkEntry } from '@/lib/benchmark-data'; // Import hardcoded data

// Initialize Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  }
})

interface BenchmarkPoint {
  target_date: string;
  target_value: number;
}

const TELEGRAM_BENCHMARK_OFFSET = 28752;
const TWITTER_BENCHMARK_OFFSET = 30155;

// Fetch benchmark data for the requested time period using hardcoded data
const getBenchmarks = (
  platform: string,
  startDateISO?: string, // ISO string date
  endDateISO?: string    // ISO string date
): BenchmarkPoint[] => {
  const platformData: RawBenchmarkEntry[] = rawBenchmarkData;

  // Transform data for the specific platform and add offset
  const transformedData: BenchmarkPoint[] = platformData.map(entry => {
    let baseValue = 0;
    if (platform === 'twitter') {
      baseValue = entry.twitter_followers + TWITTER_BENCHMARK_OFFSET;
    } else if (platform === 'telegram') {
      baseValue = entry.telegram_members + TELEGRAM_BENCHMARK_OFFSET;
    }
    return {
      target_date: entry.date, // Already YYYY-MM-DD
      target_value: baseValue,
    };
  });

  // Filter by date range if provided
  if (startDateISO && endDateISO) {
    try {
      const rangeStart = startOfDay(parseISO(startDateISO));
      const rangeEnd = endOfDay(parseISO(endDateISO));

      if (!isValid(rangeStart) || !isValid(rangeEnd)) {
        console.error('Invalid date format for benchmark filtering:', startDateISO, endDateISO);
        return []; // Or handle error appropriately
      }
      
      return transformedData.filter(benchmark => {
        const targetDate = parseISO(benchmark.target_date); // benchmark.target_date is YYYY-MM-DD, parses to T00:00:00.000Z
        if (!isValid(targetDate)) return false;
        // Check if targetDate (start of its day) is within the interval [startOfDay(rangeStart), endOfDay(rangeEnd)]
        return isWithinInterval(targetDate, { start: rangeStart, end: rangeEnd });
      });
    } catch (e) {
      console.error('Error parsing dates for benchmark filtering:', e);
      return [];
    }
  }

  return transformedData.sort((a, b) => new Date(a.target_date).getTime() - new Date(b.target_date).getTime());
};

export async function GET(request: Request) {
  try {
    // Check if user is authenticated
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse date range from URL if provided
    const { searchParams } = new URL(request.url)
    const startDateParam = searchParams.get('startDate')
    const endDateParam = searchParams.get('endDate')
    const timeFrame = searchParams.get('timeFrame') || '1d'
    
    // Handle time frame-based filtering
    let startDate: Date | null = null
    let endDate: Date | null = null
    
    if (startDateParam && endDateParam) {
      // Use explicit date range if provided
      startDate = new Date(startDateParam)
      endDate = new Date(endDateParam)
    } else {
      // Otherwise use timeFrame
      const now = new Date()
      endDate = now
      
      switch (timeFrame) {
        case '1d':
          startDate = subDays(now, 1)
          break
        case '7d':
          startDate = subDays(now, 7)
          break
        case '30d':
          startDate = subDays(now, 30)
          break
        case '90d':
          startDate = subDays(now, 90)
          break
        default:
          startDate = subDays(now, 1) // Default to 1 day
      }
    }
    
    // Format dates for query
    const startDateISO = startDate?.toISOString()
    const endDateISO = endDate?.toISOString()
    
    // Prepare query with date filtering
    let twitterQuery = adminClient.from('twitter_stats').select('*').order('timestamp', { ascending: false })
    let telegramQuery = adminClient.from('telegram_stats').select('*').order('timestamp', { ascending: false })
    
    // Always apply date filtering
    if (startDateISO && endDateISO) {
      twitterQuery = twitterQuery.gte('timestamp', startDateISO).lte('timestamp', endDateISO)
      telegramQuery = telegramQuery.gte('timestamp', startDateISO).lte('timestamp', endDateISO)
    } else {
      // Fallback to limiting results if no date range (shouldn't happen, but just in case)
      twitterQuery = twitterQuery.limit(30)
      telegramQuery = telegramQuery.limit(30)
    }
    
    // Execute all queries
    const [twitterResponse, telegramResponse, prResponse, kolsResponse] = await Promise.all([
      twitterQuery,
      telegramQuery,
      adminClient.from('pr_campaigns').select('*').order('created_at', { ascending: false }),
      adminClient.from('kols').select('*').order('post_date', { ascending: false })
    ])

    // Check for errors
    if (twitterResponse.error) {
      console.error('Error fetching Twitter stats:', twitterResponse.error)
    }
    if (telegramResponse.error) {
      console.error('Error fetching Telegram stats:', telegramResponse.error)
    }
    if (prResponse.error) {
      console.error('Error fetching PR campaigns:', prResponse.error)
    }
    if (kolsResponse.error) {
      console.error('Error fetching KOLs:', kolsResponse.error)
    }

    // Fetch benchmark data for Twitter and Telegram
    const [twitterBenchmarks, telegramBenchmarks] = await Promise.all([
      getBenchmarks('twitter', startDateISO, endDateISO),
      getBenchmarks('telegram', startDateISO, endDateISO)
    ]);

    // Return only the data within the requested range
    return NextResponse.json({
      twitter: twitterResponse.data || [],
      telegram: telegramResponse.data || [],
      pr: prResponse.data || [],
      kols: kolsResponse.data || [],
      twitter_benchmarks: twitterBenchmarks || [],
      telegram_benchmarks: telegramBenchmarks || []
    })
  } catch (error) {
    console.error('Error in stats API route:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 