import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

export async function POST(request: Request) {
  // Verify the request is authorized
  const authHeader = request.headers.get('authorization');
  const cronSecret = process.env.CRON_SECRET;

  if (!cronSecret) {
    console.error('CRON_SECRET is not set in environment variables');
    return new Response('Server configuration error', { status: 500 });
  }

  const expectedAuth = `Bearer ${cronSecret}`;
  if (!authHeader || authHeader.trim() !== expectedAuth.trim()) {
    console.error('Invalid authorization header:', authHeader);
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    // Parse request body
    const body = await request.json();
    const { script } = body;

    if (script !== 'collect-data') {
      return new Response('Invalid script name', { status: 400 });
    }

    console.log(`Running script: ${script}...`);

    // Run the script directly
    const { stdout, stderr } = await execPromise(`bun scripts/collect-data.ts`);

    console.log('Script execution completed');
    
    if (stderr) {
      console.error('Script stderr:', stderr);
    }

    return new Response(JSON.stringify({
      success: true,
      output: stdout,
      error: stderr || null
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error running script:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
} 