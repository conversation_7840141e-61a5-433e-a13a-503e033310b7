import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';
import { NextResponse } from 'next/server';
import { extractHyperlinks } from '@/lib/sheets-utils';


// Cache control
let cachedData: any = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export async function GET() {
  try {
    // Check cache first
    const now = Date.now();
    if (cachedData && now - lastFetchTime < CACHE_DURATION) {
      return NextResponse.json(cachedData);
    }

    // Setup auth
    const serviceAccountAuth = new JWT({
      email: process.env.GOOGLE_SHEETS_CLIENT_EMAIL,
      key: process.env.GOOGLE_SHEETS_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
    });

    // Load the document
    const doc = new GoogleSpreadsheet(
      process.env.GOOGLE_SHEETS_SPREADSHEET_ID!,
      serviceAccountAuth
    );
    await doc.loadInfo();
    // Get the specific sheet
    const sheet = doc.sheetsByTitle["Organic / PR "];
    if (!sheet) {
      throw new Error(`Sheet "Organic / PR " not found`);
    }
    
    // Need to load sheet info first before accessing headers
    await sheet.loadHeaderRow();
    
    // Get sheet headers to debug
    const headers = sheet.headerValues;
    console.log('Sheet headers:', headers);
    console.log('Sheet title:', sheet.title); // Log the actual sheet title
    
    // Fetch rows from the spreadsheet
    const rows = await sheet.getRows();

    // Get hyperlink information using the Google Sheets API V4
    const hyperlinkData = await extractHyperlinks(
      process.env.GOOGLE_SHEETS_SPREADSHEET_ID!,
      'Organic / PR !A:H' // Range format: SheetName!Range
    );
    
    // Find the column index for "Links"
    const linksColumnIndex = headers.findIndex(h => h === 'Links');
    
    // Transform data for expected format
    const formattedData = rows.filter((row, index) => {
      // Skip empty rows
      return row.get('Outlet') || '';
    }).map((row, index) => {
      try {
        // Map to the actual headers shown in logs
        const outlet = row.get('Outlet') || '';
        const reach = row.get('Monthly Viewership') || '';
        const linksText = row.get('Links') || '';
        const prType = row.get('Coverage Type') || '';
        const status = row.get('Status') || '';
        const publishDate = row.get('Date Published') || '';
        const tat = row.get('TAT') || '';
        const contacted = row.get('Contacted') || '';
        
        // Get hyperlink from the extracted data (row index + 1 for header row)
        let hyperlink = null;
        if (hyperlinkData[index + 1] && hyperlinkData[index + 1][linksColumnIndex]) {
          hyperlink = hyperlinkData[index + 1][linksColumnIndex].url;
        }
        
        // If no hyperlink was found but text looks like a URL, use that as fallback
        if (!hyperlink && linksText.includes('http')) {
          // Find the start of the URL
          const startIndex = linksText.indexOf('http');
          // Find the end of the URL
          let endIndex = linksText.indexOf(' ', startIndex);
          if (endIndex === -1) {
            // If no space found, check for other common delimiters
            const delimiters = ['"', "'", ')', ']', '>'];
            for (const delimiter of delimiters) {
              const idx = linksText.indexOf(delimiter, startIndex);
              if (idx !== -1 && (endIndex === -1 || idx < endIndex)) {
                endIndex = idx;
              }
            }
            // If still not found, use entire remaining string
            if (endIndex === -1) {
              endIndex = linksText.length;
            }
          }
          hyperlink = linksText.substring(startIndex, endIndex);
          
          // Clean up the URL if it ends with punctuation
          const punctuation = [',', '.', ';', ':'];
          for (const p of punctuation) {
            if (hyperlink.endsWith(p)) {
              hyperlink = hyperlink.slice(0, -1);
            }
          }
        }
        
        console.log(`Row ${index}: Link text "${linksText}", Extracted hyperlink: "${hyperlink}"`);
        
        return {
          id: `sheet-pr-${index}`,
          outlet: outlet,
          reach: reach,
          tat: tat,
          contacted: contacted,
          pr_type: prType,
          title: linksText, // Keep the title as the display text
          status: status,
          publish_date: publishDate,
          link: hyperlink || linksText, // Use extracted hyperlink or fallback to text
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          source: 'sheets'
        };
      } catch (err) {
        console.error('Error processing row:', err);
        return {
          id: `sheet-pr-${index}`,
          outlet: '',
          reach: '',
          tat: '',
          contacted: '',
          pr_type: '',
          title: '',
          status: '',
          publish_date: '',
          link: '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          source: 'sheets'
        };
      }
    });

    // Update cache
    cachedData = formattedData;
    lastFetchTime = now;
    
    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching PR sheet data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch PR data from Google Sheets' },
      { status: 500 }
    );
  }
} 