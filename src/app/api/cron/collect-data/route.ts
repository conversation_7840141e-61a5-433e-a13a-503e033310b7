import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { TwitterApi } from 'twitter-api-v2';
import { Telegraf } from 'telegraf';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function collectTwitterData(supabase: SupabaseClient) {
  console.log('Collecting Twitter data...');
  
  const twitterApiKey = process.env.TWITTER_API_KEY;
  const twitterApiSecret = process.env.TWITTER_API_SECRET;
  const twitterAccessToken = process.env.TWITTER_ACCESS_TOKEN;
  const twitterAccessSecret = process.env.TWITTER_ACCESS_SECRET;
  const twitterAccountUsername = process.env.TWITTER_ACCOUNT_USERNAME;
  
  if (!twitterApiKey || !twitterApiSecret || !twitterAccessToken || !twitterAccessSecret || !twitterAccountUsername) {
    throw new Error('Twitter credentials not found in environment variables');
  }
  
  try {
    // Initialize Twitter client
    const twitterClient = new TwitterApi({
      appKey: twitterApiKey,
      appSecret: twitterApiSecret,
      accessToken: twitterAccessToken,
      accessSecret: twitterAccessSecret,
    });
    
    // Get user data
    const user = await twitterClient.v2.userByUsername(twitterAccountUsername, {
      'user.fields': ['public_metrics'],
    });
    
    if (!user.data) {
      throw new Error(`Twitter user '${twitterAccountUsername}' not found`);
    }
    
    const followerCount = user.data.public_metrics?.followers_count || 0;
    const timestamp = new Date().toISOString();
    
    // Store data in Supabase
    const { error } = await supabase
      .from('twitter_stats')
      .insert([{
        account_name: twitterAccountUsername,
        follower_count: followerCount,
        timestamp: timestamp,
      }]);
    
    if (error) throw error;
    
    console.log(`Twitter data collected for @${twitterAccountUsername}: ${followerCount} followers`);
    return { followerCount, timestamp };
  } catch (error) {
    console.error('Error collecting Twitter data:', error);
    throw error;
  }
}

async function collectTelegramData(supabase: SupabaseClient) {
  console.log('Collecting Telegram data...');
  
  const telegramBotToken = process.env.TELEGRAM_BOT_TOKEN;
  const telegramGroupId = process.env.TELEGRAM_GROUP_ID;
  const telegramGroupName = process.env.TELEGRAM_GROUP_NAME || 'MultiBank_io';
  
  if (!telegramBotToken || !telegramGroupId) {
    throw new Error('Telegram credentials not found in environment variables');
  }
  
  try {
    // Initialize Telegram bot
    const bot = new Telegraf(telegramBotToken);
    
    // Get chat info
    const chat = await bot.telegram.getChat(telegramGroupId);
    const memberCount = await bot.telegram.getChatMembersCount(telegramGroupId);
    const chatTitle = 'title' in chat ? chat.title : telegramGroupName;
    const timestamp = new Date().toISOString();
    
    // Store data in Supabase
    const { error } = await supabase
      .from('telegram_stats')
      .insert([{
        group_name: chatTitle,
        member_count: memberCount,
        timestamp: timestamp,
      }]);
    
    if (error) throw error;
    
    console.log(`Telegram data collected for ${chatTitle}: ${memberCount} members`);
    return { memberCount, timestamp };
  } catch (error) {
    console.error('Error collecting Telegram data:', error);
    throw error;
  }
}

export async function GET(request: Request) {
  // Verify the request is from Vercel Cron
  const authHeader = request.headers.get('authorization') || request.headers.get('x-cron-auth');
  const cronSecret = process.env.CRON_SECRET;

  if (!cronSecret) {
    console.error('CRON_SECRET is not set in environment variables');
    return new Response('Server configuration error', { status: 500 });
  }

  // More flexible authorization check
  if (!authHeader) {
    console.error('No authorization header provided');
    return new Response('Unauthorized', { status: 401 });
  }

  // Accept the header either as "Bearer <token>" or just the token itself
  const expectedBearerAuth = `Bearer ${cronSecret}`;
  const isValidAuth = authHeader === expectedBearerAuth || 
                     authHeader === cronSecret || 
                     authHeader.trim() === expectedBearerAuth.trim();
  
  if (!isValidAuth) {
    console.error('Invalid authorization. Please check your CRON_SECRET');
    // Don't log the actual secrets in production for security
    if (process.env.NODE_ENV === 'development') {
      console.error('Expected one of:', expectedBearerAuth, 'or', cronSecret);
      console.error('Received:', authHeader);
    }
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    console.log('Cron job running data collection...', new Date().toISOString());
    
    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Test connection to ensure it's working
    try {
      console.log('Testing Supabase connection...');
      const { data, error } = await supabase.from('twitter_stats').select('id').limit(1);
      if (error) {
        console.error('Supabase connection test failed:', error);
        return new Response(JSON.stringify({
          success: false,
          error: 'Supabase connection failed'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      console.log('Supabase connection successful');
    } catch (err) {
      console.error('Error testing Supabase connection:', err);
      return new Response(JSON.stringify({
        success: false,
        error: 'Supabase connection test failed'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Collect data from both services
    const [twitterData, telegramData] = await Promise.all([
      collectTwitterData(supabase).catch(err => {
        console.error('Twitter data collection failed:', err);
        return { error: err.message || 'Unknown Twitter error' };
      }),
      collectTelegramData(supabase).catch(err => {
        console.error('Telegram data collection failed:', err);
        return { error: err.message || 'Unknown Telegram error' };
      })
    ]);

    console.log('Data collection complete!', new Date().toISOString());
    console.log('Twitter data:', twitterData ? 'Success' : 'Failed');
    console.log('Telegram data:', telegramData ? 'Success' : 'Failed');

    return new Response(JSON.stringify({
      success: true,
      twitter: twitterData,
      telegram: telegramData
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error collecting data:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
} 