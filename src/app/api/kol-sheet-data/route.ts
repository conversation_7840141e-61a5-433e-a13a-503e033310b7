import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';
import { NextResponse } from 'next/server';
import { extractHyperlinks } from '@/lib/sheets-utils';


// Cache control
let cachedData: any = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export async function GET() {
  try {
    // Check cache first
    const now = Date.now();
    if (cachedData && now - lastFetchTime < CACHE_DURATION) {
      return NextResponse.json(cachedData);
    }

    // Setup auth
    const serviceAccountAuth = new JWT({
      email: process.env.GOOGLE_SHEETS_CLIENT_EMAIL,
      key: process.env.GOOGLE_SHEETS_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
    });

    // Load the document
    const doc = new GoogleSpreadsheet(
      process.env.GOOGLE_SHEETS_SPREADSHEET_ID!,
      serviceAccountAuth
    );
    await doc.loadInfo();

    // Get the specific sheet
    const sheet = doc.sheetsByTitle["KOL"];
    if (!sheet) {
      throw new Error(`Sheet "KOL" not found`);
    }
    
    // Need to load sheet info first before accessing headers
    await sheet.loadHeaderRow();
    
    // Get sheet headers to debug
    const headers = sheet.headerValues;
    console.log('KOL Sheet headers:', headers);
    console.log('KOL Sheet title:', sheet.title); // Log the actual sheet title
    
    // Fetch rows
    const rows = await sheet.getRows();
    
    // Get hyperlink information using the Google Sheets API V4
    const hyperlinkData = await extractHyperlinks(
      process.env.GOOGLE_SHEETS_SPREADSHEET_ID!,
      'KOL!A:F' // Range format: SheetName!Range
    );
    
    // Find the column index for "Deliverable Links"
    const linksColumnIndex = headers.findIndex(h => h === 'Deliverable Links');
    
    // Transform data for expected format
    const formattedData = rows.map((row, index) => {
      // We have the exact CSV headers now - use them directly
      // Headers: KOL, Followers, Date to be posted, Deliverable Links, Status
      const kolName = row.get('KOL') || '';
      const followerString = row.get('Follower Count') || ''; // Renamed from 'Followers' to match sheet
      const postDate = row.get('Date to be posted') || '';
      const deliverableLinksText = row.get('Deliverable Links') || '';
      const status = row.get('Status') || '';
      
      // --- Clean Follower Count ---
      let followersNumeric: number | null = null;
      if (typeof followerString === 'string' && followerString.trim() !== '') {
        // Handle ranges like "2k - 600k" -> take the first number?
        const firstPart = followerString.split('-')[0].trim();
        // Remove spaces, handle 'K'/'k' and 'M'/'m', handle comma as decimal
        const cleanedString = firstPart
          .replace(/\s/g, '') // Remove spaces
          .replace(/,/g, '.') // Replace comma with period for decimal
          .toUpperCase(); // Make K/M uppercase

        let multiplier = 1;
        if (cleanedString.endsWith('K')) {
          multiplier = 1000;
        } else if (cleanedString.endsWith('M')) {
          multiplier = 1000000;
        }

        // Remove K/M suffix for parsing
        const numberPart = cleanedString.replace(/[KM]$/, '');

        const parsed = parseFloat(numberPart);
        if (!isNaN(parsed)) {
          followersNumeric = Math.round(parsed * multiplier);
        }
      }
      // ---------------------------
      
      // Get hyperlink from the extracted data (row index + 1 for header row)
      let hyperlink = null;
      if (hyperlinkData[index + 1] && hyperlinkData[index + 1][linksColumnIndex]) {
        hyperlink = hyperlinkData[index + 1][linksColumnIndex].url;
      }
      
      // If no hyperlink was found but text looks like a URL, use that as fallback
      if (!hyperlink && deliverableLinksText.includes('http')) {
        // If multiple links, just use the first one
        if (deliverableLinksText.includes(' ')) {
          const parts = deliverableLinksText.split(' ');
          const firstLink = parts.find((part: string) => part.startsWith('http'));
          if (firstLink) {
            let cleanLink = firstLink;
            // Clean up the URL if it ends with punctuation
            const punctuation = [',', '.', ';', ':'];
            for (const p of punctuation) {
              if (cleanLink.endsWith(p)) {
                cleanLink = cleanLink.slice(0, -1);
              }
            }
            hyperlink = cleanLink;
          }
        } else {
          // Single link in text
          // Find the start of the URL
          const startIndex = deliverableLinksText.indexOf('http');
          // Find the end of the URL (using common delimiters)
          let endIndex = -1;
          const delimiters = [' ', '"', "'", ')', ']', '>'];
          for (const delimiter of delimiters) {
            const idx = deliverableLinksText.indexOf(delimiter, startIndex);
            if (idx !== -1 && (endIndex === -1 || idx < endIndex)) {
              endIndex = idx;
            }
          }
          // If no delimiter found, use the entire remaining string
          if (endIndex === -1) {
            endIndex = deliverableLinksText.length;
          }
          
          hyperlink = deliverableLinksText.substring(startIndex, endIndex);
          
          // Clean up the URL if it ends with punctuation
          const punctuation = [',', '.', ';', ':'];
          for (const p of punctuation) {
            if (hyperlink.endsWith(p)) {
              hyperlink = hyperlink.slice(0, -1);
            }
          }
        }
      }
      
      console.log(`KOL Row ${index}: Link text "${deliverableLinksText}", Extracted hyperlink: "${hyperlink}"`);
      
      return {
        id: `sheet-kol-${index}`,
        account_handle: kolName,
        followers: followersNumeric, // Send the numeric value (or null)
        post_date: postDate,
        tweet_link: hyperlink || deliverableLinksText, // Use extracted link or fallback to text
        status: status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        source: 'sheets'
      };
    });

    // Update cache
    cachedData = formattedData;
    lastFetchTime = now;
    
    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching KOL sheet data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch KOL data from Google Sheets' },
      { status: 500 }
    );
  }
} 