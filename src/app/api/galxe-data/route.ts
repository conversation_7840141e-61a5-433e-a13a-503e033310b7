import { NextResponse } from 'next/server';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';

interface GalxeDailyAggregate {
  date: string;
  entrants: number;
  emails: number;
  verified: number;
  kyc: number;
}

const parseVerification = (value: any): number => {
  // Handle null, undefined, or genuinely empty strings as 0
  if (value === null || value === undefined || String(value).trim() === '') {
    return 0;
  }

  // Convert to string, trim whitespace, and convert to lowercase for consistent comparison
  const strValue = String(value).trim().toLowerCase();

  // Check for common "true" values
  if (strValue === '1' || strValue === 'true' || strValue === 'yes') {
    return 1;
  }

  return 0; // Default to 0 if not a recognized "true" value
};

export async function GET(request: Request) {
  try {
    // Google Sheets Auth
    let serviceAccountAuth: JWT;
    try {
      serviceAccountAuth = new JWT({
        email: process.env.GOOGLE_SHEETS_CLIENT_EMAIL,
        key: process.env.GOOGLE_SHEETS_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
      });
    } catch (e) {
      console.error('Error creating Google JWT auth:', e);
      return NextResponse.json({ error: 'Auth error' }, { status: 500 });
    }

    let doc: GoogleSpreadsheet;
    try {
      doc = new GoogleSpreadsheet(
        process.env.GALXE_SPREADSHEET_ID!,
        serviceAccountAuth
      );
    } catch (e) {
      console.error('Error creating GoogleSpreadsheet instance:', e);
      return NextResponse.json({ error: 'Spreadsheet init error' }, { status: 500 });
    }

    const sheetName = process.env.GALXE_SHEET_NAME || "Galxe";
    let sheet;
    try {
      await doc.loadInfo();
      sheet = doc.sheetsByTitle[sheetName];
      if (!sheet) {
        return NextResponse.json({ error: `Sheet "${sheetName}" not found` }, { status: 500 });
      }
    } catch (e) {
      console.error('Error loading spreadsheet info or sheet:', e);
      return NextResponse.json({ error: 'Spreadsheet load error' }, { status: 500 });
    }

    const rows = await sheet.getRows();

    // Aggregate by date
    const dailyMap: Record<string, GalxeDailyAggregate> = {};
    let rowCountForLogging = 0; // Logger

    for (const row of rows) {
      const date = row.get('createdat')?.split(' ')[0];
      if (!date) continue;

      if (rowCountForLogging < 5) { // Log first 5 rows for debugging
        console.log(`--- Row ${rowCountForLogging + 1} ---`);
        const rawEmail = row.get('Email Verified #');
        const parsedEmail = parseVerification(rawEmail);
        console.log(`Raw Email Verified #: '${rawEmail}', Parsed: ${parsedEmail}`);

        const rawPhone = row.get('Phone Verified #');
        const parsedPhone = parseVerification(rawPhone);
        console.log(`Raw Phone Verified #: '${rawPhone}', Parsed: ${parsedPhone}`);

        const rawKyc = row.get('KYC Completed #');
        const parsedKyc = parseVerification(rawKyc);
        console.log(`Raw KYC Completed #: '${rawKyc}', Parsed: ${parsedKyc}`);
        rowCountForLogging++;
      }

      if (!dailyMap[date]) {
        dailyMap[date] = { date, entrants: 0, emails: 0, verified: 0, kyc: 0 };
      }
      dailyMap[date].entrants += 1;
      dailyMap[date].emails += parseVerification(row.get('Email Verified #'));
      dailyMap[date].verified += parseVerification(row.get('Phone Verified #'));
      dailyMap[date].kyc += parseVerification(row.get('KYC Completed #'));
    }

    // Convert to sorted array
    const data: GalxeDailyAggregate[] = Object.values(dailyMap).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return NextResponse.json(data);

  } catch (error) {
    console.error('Unexpected error fetching Galxe data:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred processing the request' },
      { status: 500 }
    );
  }
}