'use client';

import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { DashboardCard } from '@/components/dashboard-card';
import { formatChartDate, getChangeInfo, formatNumber } from '@/lib/utils';
import {
  useTwitterStats,
  useTelegramStats,
  usePRCampaigns,
  useKOLs
} from '@/lib/supabase-client';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FeatureSection } from '@/components/feature-section';
import { Button } from '@/components/ui/button';
import { ModeToggle } from '@/components/mode-toggle';
import {
  BarChart3, RefreshCw, Twitter, MessageCircle, Users,
  TrendingUp, BarChart, AlertTriangle, ArrowUpRight,
  TrendingDown, Activity, Link as LinkIcon, CalendarIcon,
  Eye, EyeOff, Filter, Target, ExternalLink, CalendarDays
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { DateRangePicker } from '@/components/date-range-picker';
import { DateRange } from 'react-day-picker';
import { format, startOfToday, subDays, differenceInDays } from 'date-fns';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { collectDataAction } from '@/lib/actions';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TimeSeriesChart, TimeSeriesData, BenchmarkData as ChartBenchmarkData } from '@/components/time-series-chart';
import { ResponsiveContainer, ComposedChart, CartesianGrid, XAxis, YAxis, Tooltip, Legend, Line } from 'recharts';
import { toast } from "sonner";

// Types for data (API response)
interface TwitterStat {
  timestamp: string;
  account_name: string;
  follower_count: number;
  id: string;
  user_id?: string;
}

interface TelegramStat {
  timestamp: string;
  group_name: string;
  member_count: number;
  id: string;
  user_id?: string;
}

interface PRCampaign {
  id: string;
  outlet: string;
  pr_type: string;
  link: string;
  created_at: string;
  updated_at: string;
  publish_date?: string;
  title?: string;
}

interface KOL {
  id: string;
  account_handle: string;
  tweet_link: string;
  post_date: string;
  created_at: string;
  updated_at: string;
  followers?: number | null;
  status?: string;
}

// Benchmark data type (used for state)
interface BenchmarkData {
  target_date: string;
  target_value: number;
}

// Helper to parse date in DD/MM/YYYY format
const parseDate = (dateStr?: string): Date => {
  if (!dateStr) return new Date(); // Default to today for empty dates
  
  try {
    // Parse date in format DD/MM/YYYY
    const parts = dateStr.split('/');
    if (parts.length === 3) {
      // Format is Day/Month/Year
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
      const year = parseInt(parts[2], 10);
      
      const date = new Date(year, month, day);
      
      // Check if valid date
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
    
    // Fallback: try standard JS date parsing
    return new Date(dateStr);
  } catch (e) {
    console.error('Error parsing date:', dateStr, e);
    return new Date();
  }
};

export default function Dashboard() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [rawTwitterData, setRawTwitterData] = useState<TimeSeriesData[]>([]);
  const [rawTelegramData, setRawTelegramData] = useState<TimeSeriesData[]>([]);
  // Restore full state for latest stats
  const [latestTwitterStats, setLatestTwitterStats] = useState<{
    follower_count: number;
    account_name: string;
    timestamp: string;
  } | null>(null);
  const [latestTelegramStats, setLatestTelegramStats] = useState<{
    member_count: number;
    group_name: string;
    timestamp: string;
  } | null>(null);

  const [refreshing, setRefreshing] = useState(false);
  const [prCampaigns, setPRCampaigns] = useState<PRCampaign[]>([]);
  const [kols, setKOLs] = useState<KOL[]>([]);
  const [twitterBenchmarks, setTwitterBenchmarks] = useState<ChartBenchmarkData[]>([]);
  const [telegramBenchmarks, setTelegramBenchmarks] = useState<ChartBenchmarkData[]>([]);
  const [galxeData, setGalxeData] = useState<any[]>([]);
  const [galxeLoading, setGalxeLoading] = useState<boolean>(true);
  const [galxeTotals, setGalxeTotals] = useState<{entrants: number, emails: number, verified: number, kyc: number}>({
    entrants: 0, // Added entrants
    emails: 0,
    verified: 0,
    kyc: 0
  });
  // State for the ratio chart data
  const [galxeRatioData, setGalxeRatioData] = useState<any[]>([]);

  const [sharedTimeRange, setSharedTimeRange] = useState<string>("30d");
  const [sharedDateRange, setSharedDateRange] = useState<DateRange | undefined>(undefined);
  const [useSharedDateRange, setUseSharedDateRange] = useState(false);
  const [showTotalCounts, setShowTotalCounts] = useState(true);

  const handleSharedDateRangeChange = (range: DateRange | undefined) => {
    setSharedDateRange(range);
    if (range?.from && range?.to) { setUseSharedDateRange(true); }
    else if (range === undefined) { setUseSharedDateRange(false); }
  };

  const handleSharedTimeRangeChange = (value: string) => {
    setSharedTimeRange(value);
    setUseSharedDateRange(false);
    setSharedDateRange(undefined);
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const url = '/api/stats';
      const params = new URLSearchParams();

      // Ensure we have a proper time range for comparison
      if (useSharedDateRange && sharedDateRange?.from && sharedDateRange?.to) {
        params.append('startDate', sharedDateRange.from.toISOString());
        params.append('endDate', sharedDateRange.to.toISOString());
      } else {
        // Default to comparing with previous period
        const now = new Date();
        const timeFrameDays = sharedTimeRange === '7d' ? 7 : 
                            sharedTimeRange === '30d' ? 30 : 
                            sharedTimeRange === '90d' ? 90 : 1;
        
        // Include previous period for comparison
        const startDate = new Date(now);
        startDate.setDate(startDate.getDate() - (timeFrameDays * 2)); // Double the period for comparison
        
        params.append('startDate', startDate.toISOString());
        params.append('endDate', now.toISOString());
        params.append('timeFrame', sharedTimeRange);
      }

      const apiUrl = `${url}?${params.toString()}`;
      const response = await fetch(apiUrl);
      if (!response.ok) throw new Error(`API error: ${response.status}`);
      const data = await response.json();

      // Process Twitter stats with proper date handling
      if (data.twitter && data.twitter.length > 0) {
        setLatestTwitterStats(data.twitter[0]);
        const twitterChartData: TimeSeriesData[] = data.twitter
          .map((stat: TwitterStat) => ({
            date: stat.timestamp,
            value: stat.follower_count
          }))
          .sort((a: TimeSeriesData, b: TimeSeriesData) => 
            new Date(a.date).getTime() - new Date(b.date).getTime()
          );
        setRawTwitterData(twitterChartData);
      } else {
        setRawTwitterData([]);
        setLatestTwitterStats(null);
      }

      // Process Telegram stats with proper date handling
      if (data.telegram && data.telegram.length > 0) {
        setLatestTelegramStats(data.telegram[0]);
        const telegramChartData: TimeSeriesData[] = data.telegram
          .map((stat: TelegramStat) => ({
            date: stat.timestamp,
            value: stat.member_count
          }))
          .sort((a: TimeSeriesData, b: TimeSeriesData) => 
            new Date(a.date).getTime() - new Date(b.date).getTime()
          );
        setRawTelegramData(telegramChartData);
      } else {
        setRawTelegramData([]);
        setLatestTelegramStats(null);
      }

      // Process benchmarks
      if (data.twitter_benchmarks && Array.isArray(data.twitter_benchmarks)) {
        const formattedBenchmarks: ChartBenchmarkData[] = data.twitter_benchmarks
          .map((item: any) => ({
            target_date: item.target_date,
            target_value: item.target_value
          }))
          .filter((item: ChartBenchmarkData) => 
            item.target_date && typeof item.target_value === 'number'
          )
          .sort((a: ChartBenchmarkData, b: ChartBenchmarkData) => 
            new Date(a.target_date).getTime() - new Date(b.target_date).getTime()
          );
        setTwitterBenchmarks(formattedBenchmarks);
      } else {
        setTwitterBenchmarks([]);
      }

      if (data.telegram_benchmarks && Array.isArray(data.telegram_benchmarks)) {
        const formattedBenchmarks: ChartBenchmarkData[] = data.telegram_benchmarks
          .map((item: any) => ({
            target_date: item.target_date,
            target_value: item.target_value
          }))
          .filter((item: ChartBenchmarkData) => 
            item.target_date && typeof item.target_value === 'number'
          )
          .sort((a: ChartBenchmarkData, b: ChartBenchmarkData) => 
            new Date(a.target_date).getTime() - new Date(b.target_date).getTime()
          );
        setTelegramBenchmarks(formattedBenchmarks);
      } else {
        setTelegramBenchmarks([]);
      }

      setPRCampaigns(data.pr || []);
      setKOLs(data.kols || []);

    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load data. Please check your connection and try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchGalxeData = async () => {
    try {
      setGalxeLoading(true);
      // Construct API URL with date range if applicable
      const url = '/api/galxe-data';
      const params = new URLSearchParams();
      if (useSharedDateRange && sharedDateRange?.from && sharedDateRange?.to) {
        params.append('startDate', sharedDateRange.from.toISOString());
        params.append('endDate', sharedDateRange.to.toISOString());
      }
      // Note: We don't add a default timeFrame like '30d' here,
      // the API will fetch all data if no range is specified.
      const apiUrl = params.toString() ? `${url}?${params.toString()}` : url;
      
      const response = await fetch(apiUrl);
      if (!response.ok) throw new Error('Failed to fetch Galxe data');
      const data = await response.json();
      console.log('[Fetch Galxe] Raw API Response Data:', data); // Log the raw data structure
      // Ensure data is an array before setting state and reducing
      if (Array.isArray(data)) {
        setGalxeData(data);
      } else {
        console.error('[Fetch Galxe] API did not return an array:', data);
        setGalxeData([]); // Set to empty array to prevent chart errors
        // Optionally throw an error or set an error state here
        // throw new Error('Galxe API did not return expected data format');
      }
      
      // Calculate totals
      // Calculate cumulative totals from the daily aggregated data
      let cumulativeTotals = { entrants: 0, emails: 0, verified: 0, kyc: 0 };
      if (Array.isArray(data)) {
        cumulativeTotals = data.reduce((acc, dailyData, index) => {
          console.log(`[Reduce Galxe] Iteration ${index}:`, { dailyData, currentAcc: acc }); // Log each iteration
          // Ensure dailyData and its properties are valid numbers before adding
          const entrants = typeof dailyData?.entrants === 'number' ? dailyData.entrants : 0;
          const emails = typeof dailyData?.emails === 'number' ? dailyData.emails : 0;
          const verified = typeof dailyData?.verified === 'number' ? dailyData.verified : 0;
          const kyc = typeof dailyData?.kyc === 'number' ? dailyData.kyc : 0;

          if (typeof dailyData?.entrants !== 'number' || typeof dailyData?.emails !== 'number' || typeof dailyData?.verified !== 'number' || typeof dailyData?.kyc !== 'number') {
             console.warn(`[Reduce Galxe] Invalid data types found in iteration ${index}:`, dailyData);
          }
          
          const nextAcc = {
            entrants: acc.entrants + entrants,
            emails: acc.emails + emails,
            verified: acc.verified + verified,
            kyc: acc.kyc + kyc
          };
          // console.log(`[Reduce Galxe] Iteration ${index} - Next Acc:`, nextAcc); // Keep this commented unless deep debugging needed
          return nextAcc;
        }, { entrants: 0, emails: 0, verified: 0, kyc: 0 });
      } else {
         // If data is not an array, set totals to zero
         console.warn('[Fetch Galxe] Data is not an array, setting totals to zero.');
         cumulativeTotals = { entrants: 0, emails: 0, verified: 0, kyc: 0 };
      }
      setGalxeTotals(cumulativeTotals);
    } catch (error) {
       console.error('[Fetch Galxe] Error during fetch or processing:', error); // Enhanced error logging
    } finally {
      setGalxeLoading(false);
    }
  };

  // Function to fetch PR data directly from the PR API endpoint
  const fetchPRData = async () => {
    try {
      // Add a cache-busting parameter to bypass potential cache
      const timestamp = Date.now();
      const response = await fetch(`/api/pr-sheet-data?_=${timestamp}`);
      if (!response.ok) throw new Error(`API error: ${response.status}`);
      const data = await response.json();
      // Update PR campaigns directly
      setPRCampaigns(data || []);
    } catch (err) {
      console.error('Error fetching PR data:', err);
    }
  };

  // Function to fetch KOL data directly 
  const fetchKOLData = async () => {
    try {
      // Add a cache-busting parameter to bypass potential cache
      const timestamp = Date.now();
      const response = await fetch(`/api/kol-sheet-data?_=${timestamp}`);
      if (!response.ok) throw new Error(`API error: ${response.status}`);
      const data = await response.json();
      // Update KOLs directly
      setKOLs(data || []);
    } catch (err) {
      console.error('Error fetching KOL data:', err);
    }
  };

  useEffect(() => {
    fetchData();
    fetchGalxeData();
    // Also fetch PR and KOL data specifically to ensure it's up to date
    fetchPRData();
    fetchKOLData();
    const interval = setInterval(fetchData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [sharedTimeRange, useSharedDateRange, sharedDateRange?.from, sharedDateRange?.to]);

  const handleRefresh = async () => {
    console.log('[handleRefresh] Starting refresh process...');
    try {
      setRefreshing(true);
      try {
        const cronSecret = process.env.NEXT_PUBLIC_CRON_SECRET || '';
        console.log('[handleRefresh] Calling collectDataAction with secret:', cronSecret ? 'Provided' : 'MISSING!');
        await collectDataAction(cronSecret);
        console.log('[handleRefresh] collectDataAction call completed.');
        
        // Wait a moment for the script to potentially execute before refetching
        await new Promise(resolve => setTimeout(resolve, 2000)); 
        console.log('[handleRefresh] Continuing after wait...');
      } catch (err) {
        console.error('[handleRefresh] Error calling collectDataAction:', err);
        // Display a toast notification for the user
        toast.error("Refresh Error", {
          description: "Failed to trigger data collection. Please try again later."
        });
      }

      console.log('[handleRefresh] Fetching updated dashboard data...');
      // Fetch updated data regardless of whether the action succeeded, 
      // as the cron might have run separately.
      await Promise.all([
        fetchData(), 
        fetchGalxeData(), 
        fetchPRData(),
        fetchKOLData()
      ]);
    } catch (err) {
      console.error('[handleRefresh] Error during data fetch:', err);
      toast.error("Data Fetch Error", {
        description: "Failed to fetch updated dashboard data."
      });
      // Optionally set an error state for the UI
    } finally {
      setRefreshing(false);
      console.log('[handleRefresh] Refresh process finished.');
    }
  };

  // Moved getDateRangeDescription BEFORE getChangeDescription
  const getDateRangeDescription = (): string => {
     if (useSharedDateRange && sharedDateRange?.from && sharedDateRange?.to) {
      const start = format(sharedDateRange.from, 'MMM d');
      const end = format(sharedDateRange.to, 'MMM d');
      if (start === end) return `on ${start}`;
      return `from ${start} to ${end}`;
    }
    switch (sharedTimeRange) {
      case '7d': return 'Last 7 days';
      case '30d': return 'Last 30 days';
      case '90d': return 'Last 90 days';
      case '1d': default: return 'Last 24 hours';
    }
  };

  // Helper for DashboardCard description
  const getChangeDescription = (change: number): { text: string; color: string } => {
      const rangeDesc = getDateRangeDescription();
      if (change > 0) return { text: `+${change}% ${rangeDesc}`, color: 'text-emerald-500' };
      if (change < 0) return { text: `${change}% ${rangeDesc}`, color: 'text-red-500' };
      return { text: `No change ${rangeDesc}`, color: 'text-muted-foreground' };
  };

  // Calculate overall trend percentage based on raw data
  const calculatePercentChange = (rawData: TimeSeriesData[]): number => {
    console.log('Calculating percent change for data:', rawData);
    
    if (!Array.isArray(rawData) || rawData.length < 2) {
        console.log('Insufficient data points for percentage calculation');
        return 0;
    }

    // Sort data by date in ascending order
    const sortedData = [...rawData].sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Get the oldest and newest values within the time range
    const oldestValue = sortedData[0].value;
    const newestValue = sortedData[sortedData.length - 1].value;
    
    console.log('Calculation values:', {
        oldestValue,
        newestValue,
        timeRange: {
            start: new Date(sortedData[0].date).toISOString(),
            end: new Date(sortedData[sortedData.length - 1].date).toISOString()
        }
    });

    // Handle edge cases
    if (typeof oldestValue !== 'number' || typeof newestValue !== 'number') {
        console.log('Invalid value types detected');
        return 0;
    }
    
    if (oldestValue === 0) {
        console.log('Starting value is 0, special case handling');
        return newestValue === 0 ? 0 : 100;
    }

    // Calculate absolute change
    const absoluteChange = newestValue - oldestValue;
    
    // Calculate percentage change relative to starting point
    const percentageChange = (absoluteChange / Math.abs(oldestValue)) * 100;
    
    console.log('Change calculation:', {
        absoluteChange,
        percentageChange,
        roundedPercentage: Math.round(percentageChange)
    });
    
    return isNaN(percentageChange) ? 0 : Math.round(percentageChange);
  };

  // Calculate absolute growth based on raw data
  const calculateAbsoluteGrowth = (rawData: TimeSeriesData[], platform: string): number => {
      console.log(`[Growth Calc - ${platform}] Input Data:`, rawData); // Log input to growth calc
      // Add more robust checks
      if (!Array.isArray(rawData) || rawData.length < 2) {
          console.log(`[Growth Calc - ${platform}] Insufficient data points (< 2), returning 0.`);
          return 0;
      }
      
      const firstValue = rawData[0].value;
      const lastValue = rawData[rawData.length - 1].value;
      console.log(`[Growth Calc - ${platform}] First Value: ${firstValue} (Date: ${rawData[0].date}), Last Value: ${lastValue} (Date: ${rawData[rawData.length - 1].date})`); // Log first/last values

      // Ensure values are numbers
      if (typeof firstValue !== 'number' || typeof lastValue !== 'number') {
          console.log(`[Growth Calc - ${platform}] First or Last value is not a number, returning 0.`);
          return 0;
      }
      
      const growth = lastValue - firstValue;
      console.log(`[Growth Calc - ${platform}] Calculated Growth: ${growth}`); // Log calculated growth
      return isNaN(growth) ? 0 : growth; // Ensure result is not NaN
  };

  // Filter benchmarks for the selected time period
  // Helper function to get the date range based on selection
  const getDateRange = (): { startDate: Date | null; endDate: Date | null } => {
    let startDate: Date | null = null;
    let endDate: Date | null = null;
    
    if (useSharedDateRange && sharedDateRange?.from && sharedDateRange?.to) {
      startDate = sharedDateRange.from;
      endDate = sharedDateRange.to;
    } else {
      const now = new Date();
      endDate = now;
      switch (sharedTimeRange) {
        case '7d': startDate = subDays(now, 7); break;
        case '30d': startDate = subDays(now, 30); break;
        case '90d': startDate = subDays(now, 90); break;
        case '1d': default: startDate = subDays(now, 1); break;
      }
    }
    return { startDate, endDate };
  };

  // Filter benchmarks based on the selected time period
  const filteredBenchmarks = useMemo(() => {
    const { startDate, endDate } = getDateRange();
    if (!startDate || !endDate) return { twitter: [], telegram: [] };

    const filterByTimeRange = (benchmarks: ChartBenchmarkData[]) => {
      // Ensure benchmarks is an array before filtering
      if (!Array.isArray(benchmarks)) return []; 
      return benchmarks.filter(benchmark => {
        // Ensure benchmark and target_date are valid
        if (!benchmark || typeof benchmark.target_date !== 'string') return false; 
        const benchmarkDate = new Date(benchmark.target_date);
        return !isNaN(benchmarkDate.getTime()) && benchmarkDate >= startDate! && benchmarkDate <= endDate!;
      });
    };
    
    return {
      twitter: filterByTimeRange(twitterBenchmarks),
      telegram: filterByTimeRange(telegramBenchmarks)
    };
  }, [twitterBenchmarks, telegramBenchmarks, sharedTimeRange, useSharedDateRange, sharedDateRange]);

  // Also filter the rawData to only show the selected time range
  // Calculate baseline values for the selected period
  const baselines = useMemo(() => {
    const { startDate, endDate } = getDateRange();
    if (!startDate || !endDate) return { twitter: 0, telegram: 0 };

    const calculateBaseline = (rawData: TimeSeriesData[]) => {
      // Add check for valid array
      if (!Array.isArray(rawData)) return 0;
      const filteredData = rawData.filter(item => {
        // Add more robust check for valid item date and value
        if (!item || typeof item.date !== 'string' || typeof item.value !== 'number') return false;
        const itemDate = new Date(item.date);
        return !isNaN(itemDate.getTime()) && itemDate >= startDate! && itemDate <= endDate!;
      });
      return filteredData[0]?.value || 0;
    };

    return {
      twitter: calculateBaseline(rawTwitterData),
      telegram: calculateBaseline(rawTelegramData)
    };
  }, [rawTwitterData, rawTelegramData, sharedTimeRange, useSharedDateRange, sharedDateRange]);

  // Filter and process chart data (Total vs Growth)
  const filteredChartData = useMemo(() => {
    const { startDate, endDate } = getDateRange();
    console.log('[Filtering] Date Range:', { startDate, endDate }); // Log the date range
    if (!startDate || !endDate) return { twitter: { data: [] }, telegram: { data: [] } };

    const filterTimeSeriesData = (data: TimeSeriesData[], platform: string): TimeSeriesData[] => {
       // Add check for valid array
      if (!Array.isArray(data)) return [];
      console.log(`[Filtering] Raw ${platform} Data Before Filter:`, data); // Log raw data before filter
      const filtered = data.filter(item => {
         // Add more robust check for valid item date and value
        if (!item || typeof item.date !== 'string' || typeof item.value !== 'number') return false;
        const itemDate = new Date(item.date);
        const isValid = !isNaN(itemDate.getTime()) && itemDate >= startDate! && itemDate <= endDate!;
        // Optional: Uncomment below to log every single item check
        // console.log(`[Filtering] Item ${item.date} (${item.value}) - Valid: ${isValid}`);
        return isValid;
      });
      console.log(`[Filtering] Filtered ${platform} Data:`, filtered); // Log filtered data
      return filtered;
    };
    
    const processData = (rawData: TimeSeriesData[], baseline: number, platform: string) => {
      const filteredData = filterTimeSeriesData(rawData, platform);
      if (filteredData.length === 0) return { data: [] };
      
      if (showTotalCounts) {
        return { data: filteredData };
      } else {
        // Calculate growth relative to the baseline
        const growthData = filteredData.map(item => ({ ...item, value: Math.max(0, item.value - baseline) }));
        return { data: growthData };
      }
    };
    
    return {
      twitter: processData(rawTwitterData, baselines.twitter, 'Twitter'),
      telegram: processData(rawTelegramData, baselines.telegram, 'Telegram')
    };
  }, [rawTwitterData, rawTelegramData, showTotalCounts, sharedTimeRange, useSharedDateRange, sharedDateRange, baselines]);

  // Adjust benchmarks based on whether showing Total or Growth
  const adjustedBenchmarks = useMemo(() => {
    if (showTotalCounts) {
      // For "Total" view, display raw (filtered) benchmark values directly.
      return filteredBenchmarks;
    } else {
      // For "Growth" view, adjust benchmarks relative to their OWN baseline at the start of the period.
      const calculateGrowthFromOwnBaseline = (benchmarks: ChartBenchmarkData[]) => {
        if (!Array.isArray(benchmarks) || benchmarks.length === 0) return [];
        
        // The 'benchmarks' array (e.g., filteredBenchmarks.twitter) is already sorted
        // and filtered for the current time range by 'filteredBenchmarks'.
        // The baseline for benchmark growth is the target_value of the first benchmark in this visible range.
        const benchmarkOwnBaseline = typeof benchmarks[0].target_value === 'number' ? benchmarks[0].target_value : 0;

        return benchmarks.map(b => ({
          ...b,
          target_value: Math.max(0, (typeof b.target_value === 'number' ? b.target_value : 0) - benchmarkOwnBaseline)
        }));
      };

      return {
        twitter: calculateGrowthFromOwnBaseline(filteredBenchmarks.twitter),
        telegram: calculateGrowthFromOwnBaseline(filteredBenchmarks.telegram)
      };
    }
  }, [filteredBenchmarks, showTotalCounts]); // 'baselines' is no longer needed for this calculation

  // Update the percentage calculations to use the filtered data
  const twitterPercentChange = useMemo(() =>
    calculatePercentChange(filteredChartData.twitter.data),
    [filteredChartData.twitter.data]
  );
  
  const telegramPercentChange = useMemo(() =>
    calculatePercentChange(filteredChartData.telegram.data),
    [filteredChartData.telegram.data]
  );
  
  const twitterAbsoluteGrowth = useMemo(() =>
    calculateAbsoluteGrowth(filteredChartData.twitter.data, 'Twitter'), // Pass platform name
    [filteredChartData.twitter.data]
  );
  
  const telegramAbsoluteGrowth = useMemo(() =>
    calculateAbsoluteGrowth(filteredChartData.telegram.data, 'Telegram'), // Pass platform name
    [filteredChartData.telegram.data]
  );

  const twitterChangeInfo = useMemo(() =>
    getChangeDescription(twitterPercentChange),
    [twitterPercentChange, sharedTimeRange, sharedDateRange]
  );
  
  const telegramChangeInfo = useMemo(() =>
    getChangeDescription(telegramPercentChange),
    [telegramPercentChange, sharedTimeRange, sharedDateRange]
  );

  // Calculate daily Galxe conversion ratios
  useMemo(() => {
    if (!Array.isArray(galxeData) || galxeData.length === 0) {
      setGalxeRatioData([]);
      return;
    }

    const ratios = galxeData.map(daily => {
      const entrants = daily.entrants || 0;
      const emails = daily.emails || 0;
      const verified = daily.verified || 0; // Corresponds to Phone Verified #
      const kyc = daily.kyc || 0;

      // Calculate ratios (as percentages), handle division by zero
      const emailRatio = entrants > 0 ? (emails / entrants) * 100 : 0;
      const verifiedRatio = entrants > 0 ? (verified / entrants) * 100 : 0;
      const kycRatio = entrants > 0 ? (kyc / entrants) * 100 : 0;

      return {
        date: daily.date, // Keep the date
        // Round ratios to 2 decimal places for display
        emailRatio: parseFloat(emailRatio.toFixed(2)),
        verifiedRatio: parseFloat(verifiedRatio.toFixed(2)),
        kycRatio: parseFloat(kycRatio.toFixed(2)),
      };
    });
    
    console.log("[Galxe Ratios] Calculated Ratio Data:", ratios); // Log calculated ratios
    setGalxeRatioData(ratios);

  }, [galxeData]); // Recalculate when galxeData changes

  return (
    <div className="pt-6 pb-12">
      <div className="bg-muted/40 py-8 border-b">
         <div className="container max-w-7xl mx-auto px-4 sm:px-8">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div><h1 className="text-2xl sm:text-3xl font-bold tracking-tight mb-2">Social Media Tracker</h1><p className="text-muted-foreground text-sm sm:text-base">Track your social media followers and engagement metrics</p></div>
            <div className="flex items-center gap-2 sm:gap-4 mt-4 sm:mt-0 w-full sm:w-auto justify-between sm:justify-end">
              <ModeToggle />
              <Button onClick={handleRefresh} variant="outline" size="sm" className="gap-1.5 px-2.5 py-1.5" disabled={refreshing}><RefreshCw className={`h-3.5 w-3.5 ${refreshing ? 'animate-spin' : ''}`} /><span className="text-xs">{refreshing ? 'Refreshing...' : 'Refresh'}</span></Button>
              <Link href="/logout"><Button variant="ghost" size="sm" className="text-xs sm:text-sm px-2 sm:px-3">Log Out</Button></Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container max-w-7xl mx-auto px-4 sm:px-8 py-6 sm:py-12">
        {error && ( <div className="bg-destructive/10 border border-destructive/20 text-destructive rounded-lg p-4 mb-12"><div className="flex items-start space-x-4"><AlertTriangle className="h-5 w-5 mt-0.5" /><div><h3 className="font-medium">Error Loading Data</h3><p className="text-sm text-destructive/80 mt-1">{error}</p><Button onClick={fetchData} variant="destructive" size="sm" className="mt-4">Try Again</Button></div></div></div> )}

        {/* Stats Overview - Updated to be dynamic */}
        <div className="grid gap-4 sm:gap-8 md:grid-cols-2 mb-8 sm:mb-12">
          <DashboardCard
            title={showTotalCounts
                ? `Twitter Followers (${latestTwitterStats?.account_name || 'N/A'})`
                : `Twitter Growth (${latestTwitterStats?.account_name || 'N/A'})`
            }
            value={showTotalCounts
                // Show latest total count if available
                ? (latestTwitterStats ? latestTwitterStats.follower_count.toLocaleString() : "0")
                // Show absolute growth, formatted with sign
                : (twitterAbsoluteGrowth >= 0 ? `+${twitterAbsoluteGrowth.toLocaleString()}` : twitterAbsoluteGrowth.toLocaleString())
            }
            loading={loading}
            description={twitterChangeInfo.text} // Description always shows % change over period
            descriptionColor={twitterChangeInfo.color}
            icon={<Twitter className="h-4 w-4" />}
            colors={['#1DA1F2', '#0c89df', '#0b78c5']}
          />
          <DashboardCard
            title={showTotalCounts
                ? `Telegram Members (${latestTelegramStats?.group_name || 'N/A'})`
                : `Telegram Growth (${latestTelegramStats?.group_name || 'N/A'})`
            }
            value={showTotalCounts
                 // Show latest total count if available
                ? (latestTelegramStats ? latestTelegramStats.member_count.toLocaleString() : "0")
                 // Show absolute growth, formatted with sign
                : (telegramAbsoluteGrowth >= 0 ? `+${telegramAbsoluteGrowth.toLocaleString()}` : telegramAbsoluteGrowth.toLocaleString())
            }
            loading={loading}
            description={telegramChangeInfo.text} // Description always shows % change over period
            descriptionColor={telegramChangeInfo.color}
            icon={<MessageCircle className="h-4 w-4" />}
            colors={['#0088cc', '#0077b3', '#006699']}
          />
        </div>

        {/* Growth Trends */}
        <div className="space-y-4 mb-8 sm:mb-12">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-4">
            <h2 className="text-2xl font-bold tracking-tight">Growth Trends</h2>
            <div className="flex items-center gap-4 w-full sm:w-auto justify-end">
              {/* Restore Growth/Total Switch */}
              <div className="flex items-center space-x-2"><Label htmlFor="show-total" className="text-sm font-medium">{showTotalCounts ? 'Total' : 'Growth'}</Label><Switch id="show-total" checked={showTotalCounts} onCheckedChange={setShowTotalCounts} aria-label="Toggle between total counts and growth"/></div>
              {/* Shared Date Controls */}
              <DateRangePicker dateRange={sharedDateRange} onDateRangeChange={handleSharedDateRangeChange} />
              <Select value={useSharedDateRange ? '' : sharedTimeRange} onValueChange={handleSharedTimeRangeChange} disabled={useSharedDateRange}><SelectTrigger className="w-[120px] rounded-lg" aria-label="Select time range"><SelectValue placeholder="Select Range" /></SelectTrigger><SelectContent className="rounded-xl"><SelectItem value="90d" className="rounded-lg">Last 90d</SelectItem><SelectItem value="30d" className="rounded-lg">Last 30d</SelectItem><SelectItem value="7d" className="rounded-lg">Last 7d</SelectItem><SelectItem value="1d" className="rounded-lg">Last 1d</SelectItem></SelectContent></Select>
            </div>
          </div>

          {/* Use TimeSeriesChart */}
          <div className="grid md:grid-cols-2 gap-8">
            <TimeSeriesChart
              title={showTotalCounts ? "Twitter Followers" : "Twitter Follower Growth"}
              data={filteredChartData.twitter.data}
              benchmarks={adjustedBenchmarks.twitter}
              loading={loading}
              valueLabel={showTotalCounts ? "Followers" : "Follower Growth"}
              color="hsl(var(--chart-1))"
              positiveColor="hsl(var(--chart-positive))"
              negativeColor="hsl(var(--chart-negative))"
              benchmarkColor="#ff9800"
              percentChange={twitterPercentChange}
            />
            <TimeSeriesChart
              title={showTotalCounts ? "Telegram Members" : "Telegram Member Growth"}
              data={filteredChartData.telegram.data}
              benchmarks={adjustedBenchmarks.telegram}
              loading={loading}
              valueLabel={showTotalCounts ? "Members" : "Member Growth"}
              color="hsl(var(--chart-2))"
              positiveColor="hsl(var(--chart-positive))"
              negativeColor="hsl(var(--chart-negative))"
              benchmarkColor="#ff9800"
              percentChange={telegramPercentChange}
            />
          </div>
        </div>

        {/* Galxe Data */}
        <div className="space-y-4 mb-8 sm:mb-12">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-4">
            <h2 className="text-2xl font-bold tracking-tight">Galxe Data</h2>
          </div>
          
          {/* Galxe Totals Card */}
          <Card className="overflow-hidden border-opacity-50 w-full mb-6">
            <CardHeader className="p-4 pb-0">
              <CardTitle className="text-base font-medium">Total Registration Stats</CardTitle>
              <CardDescription>Cumulative registration metrics from Galxe campaign</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-2">
              {galxeLoading ? (
                <div className="animate-pulse space-y-3">
                  <div className="h-6 bg-muted rounded w-3/4"></div>
                  <div className="h-6 bg-muted rounded w-full"></div>
                  <div className="h-6 bg-muted rounded w-2/3"></div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-muted/30 p-4 rounded-lg text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-indigo-500">{galxeTotals.entrants.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground mt-1">Total Entrants</div>
                  </div>
                  <div className="bg-muted/30 p-4 rounded-lg text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-primary">{galxeTotals.emails.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground mt-1">Verified Emails</div>
                  </div>
                  <div className="bg-muted/30 p-4 rounded-lg text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-emerald-500">{galxeTotals.verified.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground mt-1">Verified Numbers</div>
                  </div>
                  <div className="bg-muted/30 p-4 rounded-lg text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-amber-500">{galxeTotals.kyc.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground mt-1">KYC Completed</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card className="overflow-hidden border-opacity-50 w-full">
            <CardHeader className="p-4 pb-0">
              <CardTitle className="text-base font-medium">User Acquisition Metrics</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-2">
              <div className="h-[320px]">
                {galxeLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-pulse space-y-3">
                      <div className="h-6 bg-muted rounded w-3/4"></div>
                      <div className="h-6 bg-muted rounded w-full"></div>
                      <div className="h-6 bg-muted rounded w-2/3"></div>
                    </div>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart
                      data={galxeData}
                      margin={{ top: 10, right: 30, left: 20, bottom: 40 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis 
                        dataKey="date"
                        angle={-45} 
                        textAnchor="end" 
                        height={60} 
                        tick={{ fontSize: 11 }}
                      />
                      <YAxis 
                        tickFormatter={(value) => value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value}
                        tick={{ fontSize: 11 }}
                      />
                      
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: 'var(--background)',
                          border: '1px solid var(--border)',
                          borderRadius: '6px',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                        }}
                        labelFormatter={(label) => `Date: ${label}`}
                        formatter={(value, name) => {
                          let displayName = name;
                          if (name === "entrants") displayName = "Entrants";
                          if (name === "emails") displayName = "Verified Emails";
                          if (name === "verified") displayName = "Verified Numbers";
                          if (name === "kyc") displayName = "KYC Completed";
                          return [value.toLocaleString(), displayName];
                        }}
                        separator=": "
                      />
                      
                      <Legend 
                        formatter={(value) => {
                          if (value === "entrants") return "Total Entrants";
                          if (value === "emails") return "Verified Emails";
                          if (value === "verified") return "Verified Numbers";
                          if (value === "kyc") return "KYC Completed";
                          return value;
                        }}
                      />
                      
                      {/* Added Line for Entrants */}
                      <Line
                        type="monotone"
                        dataKey="entrants"
                        stroke="#6366f1" // Indigo color
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="emails"
                        stroke="#8884d8" // Original purple
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                      {/* Updated dataKey to 'verified' for Verified Numbers */}
                      <Line
                        type="monotone"
                        dataKey="verified"
                        stroke="#82ca9d" // Original green
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="kyc"
                        stroke="#ff9800" // Original orange
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
          
          {/* Galxe Conversion Ratios Chart */}
          <Card className="overflow-hidden border-opacity-50 w-full mt-6">
            <CardHeader className="p-4 pb-0">
              <CardTitle className="text-base font-medium">User Conversion Ratios (%)</CardTitle>
              <CardDescription>Daily conversion rates relative to total entrants</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-2">
              <div className="h-[320px]">
                {galxeLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-pulse space-y-3">
                      <div className="h-6 bg-muted rounded w-3/4"></div>
                      <div className="h-6 bg-muted rounded w-full"></div>
                      <div className="h-6 bg-muted rounded w-2/3"></div>
                    </div>
                  </div>
                ) : galxeRatioData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart
                      data={galxeRatioData} // Use the calculated ratio data
                      margin={{ top: 10, right: 30, left: 0, bottom: 40 }} // Adjusted left margin
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis
                        dataKey="date"
                        angle={-45}
                        textAnchor="end"
                        height={60}
                        tick={{ fontSize: 11 }}
                      />
                      <YAxis
                        tickFormatter={(value) => `${value}%`} // Format as percentage
                        tick={{ fontSize: 11 }}
                        domain={[0, 100]} // Set Y-axis domain 0-100%
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'var(--background)',
                          border: '1px solid var(--border)',
                          borderRadius: '6px',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                        }}
                        labelFormatter={(label) => `Date: ${label}`}
                        formatter={(value: number, name: string) => {
                          let displayName = name;
                          if (name === "emailRatio") displayName = "Email Verified Rate";
                          if (name === "verifiedRatio") displayName = "Number Verified Rate";
                          if (name === "kycRatio") displayName = "KYC Completed Rate";
                          return [`${value.toFixed(2)}%`, displayName]; // Format value as percentage
                        }}
                        separator=": "
                      />
                      <Legend
                        formatter={(value) => {
                          if (value === "emailRatio") return "Email Verified Rate";
                          if (value === "verifiedRatio") return "Number Verified Rate";
                          if (value === "kycRatio") return "KYC Completed Rate";
                          return value;
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="emailRatio"
                        stroke="#8884d8" // Purple
                        strokeWidth={2}
                        dot={{ r: 3 }}
                        activeDot={{ r: 5 }}
                        name="Email Verified Rate" // Name for Legend/Tooltip
                      />
                      <Line
                        type="monotone"
                        dataKey="verifiedRatio" // Represents Phone Verified # ratio
                        stroke="#82ca9d" // Green
                        strokeWidth={2}
                        dot={{ r: 3 }}
                        activeDot={{ r: 5 }}
                        name="Number Verified Rate" // Name for Legend/Tooltip
                      />
                      <Line
                        type="monotone"
                        dataKey="kycRatio"
                        stroke="#ff9800" // Orange
                        strokeWidth={2}
                        dot={{ r: 3 }}
                        activeDot={{ r: 5 }}
                        name="KYC Completed Rate" // Name for Legend/Tooltip
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                ) : (
                   <div className="flex items-center justify-center h-full text-muted-foreground">No ratio data available for the selected period</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* PR Campaigns section */}
        <div className="space-y-4 sm:space-y-8 mb-8 sm:mb-12">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold tracking-tight">Latest PR Campaigns</h2>
            <Link href="/pr"><Button variant="outline" size="sm">View All PR Campaigns</Button></Link>
          </div>
          <Card className="border border-border/40 overflow-hidden">
            <div className="h-1 bg-primary/40 w-full"></div>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Recent Coverage</CardTitle>
              <CardDescription>The latest PR campaigns and media mentions</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? ( 
                <div className="animate-pulse space-y-3">
                  <div className="h-6 bg-muted rounded w-3/4"></div>
                  <div className="h-6 bg-muted rounded w-full"></div>
                  <div className="h-6 bg-muted rounded w-2/3"></div>
                </div> 
              ) : prCampaigns.length > 0 ? ( 
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[30%]">Outlet</TableHead>
                      <TableHead className="hidden sm:table-cell">Type</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="text-right">Link</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {prCampaigns.slice(0, 3).map((campaign) => (
                      <TableRow key={campaign.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium truncate">{campaign.outlet}</TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <Badge 
                            variant="secondary" 
                            className={`rounded-sm text-xs px-2 py-0 
                              ${campaign.pr_type === 'Leak' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400' : 
                                campaign.pr_type === 'Interview' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' : 
                                campaign.pr_type === 'Feature' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400' : 
                                'bg-muted'}`}
                          >
                            {campaign.pr_type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-xs">
                            <CalendarDays className="h-3 w-3 mr-1 text-muted-foreground" />
                            <span>{campaign.publish_date || new Date(campaign.created_at).toLocaleDateString()}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          {campaign.link && campaign.link.startsWith('http') ? (
                            <a 
                              href={campaign.link} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="inline-flex items-center gap-1 text-primary hover:underline px-2 py-1 rounded-sm hover:bg-primary/5"
                            >
                              <ExternalLink className="h-3.5 w-3.5" />
                              <span className="hidden sm:inline text-xs">View</span>
                            </a>
                          ) : campaign.title && campaign.title.startsWith('http') ? (
                            <a 
                              href={campaign.title} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="inline-flex items-center gap-1 text-primary hover:underline px-2 py-1 rounded-sm hover:bg-primary/5"
                            >
                              <ExternalLink className="h-3.5 w-3.5" />
                              <span className="hidden sm:inline text-xs">View</span>
                            </a>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table> 
              ) : ( 
                <div className="text-center py-4 text-muted-foreground">No PR campaigns available</div> 
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4 sm:space-y-8 mb-8 sm:mb-12">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold tracking-tight">Key Opinion Leaders</h2>
            <Link href="/kols"><Button variant="outline" size="sm">View All KOLs</Button></Link>
          </div>
          <Card className="border border-border/40 overflow-hidden">
            <div className="h-1 bg-purple-500/60 w-full"></div>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Recent Influencer Activity</CardTitle>
              <CardDescription>Latest social media posts from key influencers</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? ( 
                <div className="animate-pulse space-y-3">
                  <div className="h-6 bg-muted rounded w-3/4"></div>
                  <div className="h-6 bg-muted rounded w-full"></div>
                  <div className="h-6 bg-muted rounded w-2/3"></div>
                </div> 
              ) : kols.length > 0 ? ( 
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[30%]">Account</TableHead>
                      <TableHead>Followers</TableHead>
                      <TableHead>Post Date</TableHead>
                      <TableHead className="text-right">Link</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {kols.slice(0, 3).map((kol) => (
                      <TableRow key={kol.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium truncate">{kol.account_handle}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-medium text-xs bg-background/80">
                            {(kol.followers !== null && typeof kol.followers === 'number') ? formatNumber(kol.followers) : "-"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-xs">
                            <CalendarDays className="h-3 w-3 mr-1 text-muted-foreground" />
                            <span>{parseDate(kol.post_date).toLocaleDateString()}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <a 
                            href={kol.tweet_link} 
                            target="_blank" 
                            rel="noopener noreferrer" 
                            className="inline-flex items-center gap-1 text-primary hover:underline px-2 py-1 rounded-sm hover:bg-primary/5"
                          >
                            <ExternalLink className="h-3.5 w-3.5" />
                            <span className="hidden sm:inline text-xs">View Post</span>
                            <span className="sm:hidden text-xs">View</span>
                          </a>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table> 
              ) : ( 
                <div className="text-center py-4 text-muted-foreground">No KOL data available</div> 
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4 sm:space-y-8 mb-8 sm:mb-12">
          <div className="flex items-center justify-between"><h2 className="text-2xl font-bold tracking-tight">Quick Navigation</h2></div>
          <div className="grid gap-4 sm:gap-8 md:grid-cols-2">
            <Link href="/pr" className="block"><DashboardCard title="PR Dashboard" value="View PR Analytics" icon={<TrendingUp className="h-4 w-4" />} colors={['#10b981', '#059669', '#047857']} /></Link>
            <Link href="/kols" className="block"><DashboardCard title="KOLs Management" value="Manage Key Influencers" icon={<Users className="h-4 w-4" />} colors={['#8b5cf6', '#7c3aed', '#6d28d9']} /></Link>
          </div>
        </div>
      </div>
    </div>
  );
}