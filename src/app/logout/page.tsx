'use client';

import { SignOutButton } from '@clerk/nextjs';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { LogOut, ArrowLeft } from 'lucide-react';

export default function LogoutPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-6">
      <div className="w-full max-w-md border rounded-lg p-8 bg-background shadow-lg">
        <h1 className="text-2xl font-bold mb-6">Log Out</h1>
        
        <p className="mb-6 text-muted-foreground">
          Click the button below to sign out of your account.
        </p>
        
        <div className="space-y-4">
          <SignOutButton>
            <Button className="w-full" size="lg">
              <LogOut className="mr-2 h-5 w-5" />
              Sign Out
            </Button>
          </SignOutButton>
          
          <Link href="/dashboard">
            <Button className="w-full" variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
} 