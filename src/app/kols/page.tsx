'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { ArrowLeft, ExternalLink, MessageCircle, RefreshCw, CalendarDays, LogIn, LinkIcon, Search, Info } from 'lucide-react';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { formatNumber } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { DateRangePicker } from '@/components/date-range-picker';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface KOL {
  id: string;
  account_handle: string;
  tweet_link: string;
  post_date: string;
  created_at: string;
  updated_at?: string;
  user_id?: string;
  source?: string;
  outlet?: string;
  followers?: number | null;
  status?: string;
}

// Enum for sorting options
enum SortOption {
  DEFAULT = 'default',
  FOLLOWERS_HIGH_TO_LOW = 'followers_high_to_low',
  FOLLOWERS_LOW_TO_HIGH = 'followers_low_to_high',
  DATE_NEWEST = 'date_newest',
  DATE_OLDEST = 'date_oldest',
  STATUS = 'status'
}

// Enum for time filter
enum TimeFilter {
  ALL = 'all',
  LAST_WEEK = 'last_week',
  LAST_MONTH = 'last_month',
  LAST_3_MONTHS = 'last_3_months',
  CUSTOM = 'custom'
}

const TimeframeFilter = ({
  activeTimeframe,
  onChangeTimeframe,
  dateRange,
  onDateRangeChange
}: {
  activeTimeframe: string,
  onChangeTimeframe: (value: string) => void,
  dateRange?: DateRange,
  onDateRangeChange: (range: DateRange | undefined) => void
}) => {
  return (
    <div className="flex items-center gap-2">
      <label className="text-sm font-medium">Timeframe:</label>
      <Select value={activeTimeframe} onValueChange={(value) => {
        onChangeTimeframe(value);
        // Reset date range if not selecting custom
        if (value !== TimeFilter.CUSTOM) {
          onDateRangeChange(undefined);
        }
      }}>
        <SelectTrigger className="w-[120px]">
          <SelectValue placeholder="Select timeframe" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={TimeFilter.ALL}>All Time</SelectItem>
          <SelectItem value={TimeFilter.LAST_WEEK}>Last 7 Days</SelectItem>
          <SelectItem value={TimeFilter.LAST_MONTH}>Last 30 Days</SelectItem>
          <SelectItem value={TimeFilter.LAST_3_MONTHS}>Last 3 Months</SelectItem>
          <SelectItem value={TimeFilter.CUSTOM}>Custom Range</SelectItem>
        </SelectContent>
      </Select>

      {activeTimeframe === TimeFilter.CUSTOM && (
        <DateRangePicker
          dateRange={dateRange}
          onDateRangeChange={onDateRangeChange}
        />
      )}
    </div>
  );
};

const SortDropdown = ({ sortOption, onChangeSortOption }: { sortOption: string, onChangeSortOption: (value: string) => void }) => {
  return (
    <div className="flex items-center space-x-2">
      <label className="text-sm font-medium">Sort by:</label>
      <Select value={sortOption} onValueChange={onChangeSortOption}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={SortOption.FOLLOWERS_HIGH_TO_LOW}>Followers (High to Low)</SelectItem>
          <SelectItem value={SortOption.FOLLOWERS_LOW_TO_HIGH}>Followers (Low to High)</SelectItem>
          <SelectItem value={SortOption.DATE_NEWEST}>Date (Newest First)</SelectItem>
          <SelectItem value={SortOption.DATE_OLDEST}>Date (Oldest First)</SelectItem>
          <SelectItem value={SortOption.STATUS}>Status</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default function KOLsDashboard() {
  const [loading, setLoading] = useState(true);
  const [kols, setKols] = useState<KOL[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [activeTimeframe, setActiveTimeframe] = useState<string>(TimeFilter.ALL);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [refreshing, setRefreshing] = useState(false);
  const [sortOption, setSortOption] = useState<SortOption>(SortOption.DEFAULT);
  const [searchTerm, setSearchTerm] = useState('');

  // Get authentication state
  const { isLoaded, isSignedIn } = useAuth();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  const loadKOLsFromSheet = async () => {
    try {
      setLoading(true);
      setRefreshing(true);

      // Only try to fetch if user is authenticated
      if (isSignedIn) {
        // Fetch data from Google Sheets
        const response = await fetch('/api/kol-sheet-data');

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();

        // Process the data without adding placeholder values
        const processedData = data
          .filter((kol: KOL) => kol.account_handle || kol.tweet_link || kol.outlet)
          .map((kol: KOL) => ({
            ...kol,
            // Don't add placeholders, just keep the original values
            post_date: kol.post_date || ''
          }));

        setKols(processedData);
      }
    } catch (err) {
      console.error('Error fetching KOLs data:', err);
      setError('Failed to load KOLs data. Please try again later.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (isSignedIn) {
      loadKOLsFromSheet();
    }
  }, [isSignedIn]);

  // Functions to sort KOLs
  const sortKols = (kols: KOL[], sortBy: SortOption): KOL[] => {
    const kopiedKols = [...kols];

    // Helper to parse date in DD/MM/YYYY format
    const parseDate = (dateStr?: string): Date => {
      if (!dateStr) return new Date(0); // Default to epoch for empty dates

      try {
        // Parse date in format DD/MM/YYYY
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          // Format is Day/Month/Year
          const day = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
          const year = parseInt(parts[2], 10);

          const date = new Date(year, month, day);

          // Check if valid date
          if (!isNaN(date.getTime())) {
            return date;
          }
        }

        // Fallback: try standard JS date parsing
        return new Date(dateStr);
      } catch (e) {
        console.error('Error parsing date:', dateStr, e);
        return new Date(0);
      }
    };

    switch (sortBy) {
      case SortOption.FOLLOWERS_HIGH_TO_LOW:
        return kopiedKols.sort((a, b) => {
          return (b.followers ?? 0) - (a.followers ?? 0); // Directly compare numbers, handling null
        });

      case SortOption.FOLLOWERS_LOW_TO_HIGH:
        return kopiedKols.sort((a, b) => {
          return (a.followers ?? 0) - (b.followers ?? 0); // Directly compare numbers, handling null
        });

      case SortOption.DATE_NEWEST:
        return kopiedKols.sort((a, b) => {
          return parseDate(b.post_date).getTime() - parseDate(a.post_date).getTime();
        });

      case SortOption.DATE_OLDEST:
        return kopiedKols.sort((a, b) => {
          return parseDate(a.post_date).getTime() - parseDate(b.post_date).getTime();
        });

      case SortOption.STATUS:
        return kopiedKols.sort((a, b) => {
          const statusA = a.status || '';
          const statusB = b.status || '';
          return statusA.localeCompare(statusB);
        });

      default:
        return kopiedKols;
    }
  };

  // Function to filter KOLs by timeframe
  const filterKolsByTime = (kols: KOL[], timeFilter: string, customDateRange?: DateRange): KOL[] => {
    if (timeFilter === TimeFilter.ALL) {
      return kols;
    }

    // Helper to parse date in DD/MM/YYYY format
    const parseDate = (dateStr?: string): Date => {
      if (!dateStr) return new Date(0); // Default to epoch for empty dates

      try {
        // Parse date in format DD/MM/YYYY
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          // Format is Day/Month/Year
          const day = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
          const year = parseInt(parts[2], 10);

          const date = new Date(year, month, day);

          // Check if valid date
          if (!isNaN(date.getTime())) {
            return date;
          }
        }

        // Fallback: try standard JS date parsing
        return new Date(dateStr);
      } catch (e) {
        console.error('Error parsing date:', dateStr, e);
        return new Date(0);
      }
    };

    // Handle custom date range
    if (timeFilter === TimeFilter.CUSTOM && customDateRange?.from) {
      return kols.filter(kol => {
        if (!kol.post_date) return false;

        const kolDate = parseDate(kol.post_date);

        // If only 'from' date is provided
        if (customDateRange.from && !customDateRange.to) {
          return kolDate >= customDateRange.from;
        }

        // If both 'from' and 'to' dates are provided
        if (customDateRange.from && customDateRange.to) {
          // Add one day to 'to' date to include the end date in the range
          const endDate = new Date(customDateRange.to);
          endDate.setDate(endDate.getDate() + 1);

          return kolDate >= customDateRange.from && kolDate < endDate;
        }

        return false;
      });
    }

    // Handle predefined timeframes
    const now = new Date();
    const cutoffDate = new Date();

    switch (timeFilter) {
      case TimeFilter.LAST_WEEK:
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case TimeFilter.LAST_MONTH:
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case TimeFilter.LAST_3_MONTHS:
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      default:
        return kols;
    }

    return kols.filter(kol => {
      if (!kol.post_date) return false;

      const kolDate = parseDate(kol.post_date);

      // Check if valid date and within range
      return !isNaN(kolDate.getTime()) && kolDate >= cutoffDate && kolDate <= now;
    });
  };

  // Function to search KOLs
  const searchKOLs = (kols: KOL[], term: string): KOL[] => {
    if (!term) {
      return kols;
    }
    const lowerCaseTerm = term.toLowerCase();
    return kols.filter(kol =>
      (kol.account_handle && kol.account_handle.toLowerCase().includes(lowerCaseTerm)) ||
      (kol.tweet_link && kol.tweet_link.toLowerCase().includes(lowerCaseTerm)) ||
      (kol.post_date && kol.post_date.toLowerCase().includes(lowerCaseTerm)) ||
      // Check for both null and undefined before converting followers number to string for searching
      (kol.followers !== null && kol.followers !== undefined && kol.followers.toString().includes(lowerCaseTerm)) ||
      (kol.status && kol.status.toLowerCase().includes(lowerCaseTerm))
    );
  };

  // Apply sorting and filtering
  const processedKOLs = useMemo(() => {
    const timeFiltered = filterKolsByTime(kols, activeTimeframe, dateRange);
    const searchFiltered = searchKOLs(timeFiltered, searchTerm);
    return sortKols(searchFiltered, sortOption);
  }, [kols, sortOption, activeTimeframe, searchTerm, dateRange]);

  const handleRefresh = () => {
    loadKOLsFromSheet();
  };

  const groupKolsByTimeframe = (kols: KOL[]) => {
    const grouped: { [key: string]: KOL[] } = {};

    // Helper to parse date in DD/MM/YYYY format
    const parseDate = (dateStr?: string): Date => {
      if (!dateStr) return new Date(0); // Default to epoch for empty dates

      try {
        // Parse date in format DD/MM/YYYY
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          // Format is Day/Month/Year
          const day = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
          const year = parseInt(parts[2], 10);

          const date = new Date(year, month, day);

          // Check if valid date
          if (!isNaN(date.getTime())) {
            return date;
          }
        }

        // Fallback: try standard JS date parsing
        return new Date(dateStr);
      } catch (e) {
        console.error('Error parsing date:', dateStr, e);
        return new Date(0);
      }
    };

    // Sort KOLs by date first
    const sortedKols = [...kols].sort((a, b) => {
      const dateA = parseDate(a.post_date);
      const dateB = parseDate(b.post_date);
      return dateB.getTime() - dateA.getTime(); // Newest first
    });

    // Group by month and year
    sortedKols.forEach(kol => {
      if (!kol.post_date) {
        // Handle items without dates
        if (!grouped['No Date']) {
          grouped['No Date'] = [];
        }
        grouped['No Date'].push(kol);
        return;
      }

      const date = parseDate(kol.post_date);
      // Format as "Month Year" (e.g., "January 2023")
      const key = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(kol);
    });

    return grouped;
  };

  const kolsByTimeframe = groupKolsByTimeframe(processedKOLs);

  // Show loading state while checking auth
  if (!isLoaded) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin w-10 h-10 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Show auth required message if not signed in
  if (!isSignedIn) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="max-w-md w-full">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">You need to sign in to access the KOLs dashboard.</p>
            <Link href="/sign-in">
              <Button className="w-full">
                <LogIn className="mr-2 h-4 w-4" /> Sign In
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="pt-6 pb-12">
      <div className="bg-muted/40 py-8 border-b">
        <div className="container max-w-7xl mx-auto px-8">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <Link href="/dashboard" className="inline-flex items-center text-muted-foreground hover:text-foreground mb-4 px-3 py-1 rounded-md hover:bg-background/10 transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold tracking-tight mb-2">KOLs Management</h1>
              <p className="text-muted-foreground">
                Track and manage Key Opinion Leaders' social media posts
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="default" size="sm" className="space-x-2">
                <MessageCircle className="h-4 w-4" />
                <span>Add KOL Post</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container max-w-7xl mx-auto px-8 py-12">
        {error ? (
          <Card className="bg-destructive/10 border-destructive/20 mb-8">
            <CardContent className="pt-6">
              <p className="text-destructive">{error}</p>
            </CardContent>
          </Card>
        ) : (
          <>
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold tracking-tight">KOLs</h1>
                <Badge variant="outline" className="ml-2">
                  {processedKOLs.length}
                </Badge>
              </div>

              <div className="flex items-center gap-4">
                <div className="relative">
                  <Input
                    type="text"
                    placeholder="Search KOLs..."
                    value={searchTerm}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                    className="w-[250px] mr-2"
                  />
                  <Search className="absolute right-6 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>

                <TimeframeFilter
                  activeTimeframe={activeTimeframe}
                  onChangeTimeframe={setActiveTimeframe}
                  dateRange={dateRange}
                  onDateRangeChange={setDateRange}
                />

                <SortDropdown
                  sortOption={sortOption}
                  onChangeSortOption={(value) => setSortOption(value as SortOption)}
                />

                <Button onClick={handleRefresh} disabled={refreshing} variant="outline" size="sm">
                  {refreshing ? (
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  Refresh
                </Button>
              </div>
            </div>

            {loading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Card key={i} className="animate-pulse overflow-hidden border shadow-sm">
                    <div className="h-2 bg-primary/20 w-full"></div>
                    <CardHeader className="p-6">
                      <div className="h-6 w-2/3 bg-muted rounded"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-4 w-full bg-muted rounded mb-4"></div>
                      <div className="h-4 w-2/3 bg-muted rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <>
                {Object.keys(kolsByTimeframe).length === 0 ? (
                  <Card className="border-dashed border-2">
                    <CardContent className="flex flex-col items-center justify-center py-12">
                      <MessageCircle className="h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-center text-muted-foreground mb-4">
                        {activeTimeframe === 'all'
                          ? 'No KOL posts found. Add a new post to get started.'
                          : 'No KOL posts found in the selected timeframe.'}
                      </p>
                      <Button variant="outline" size="sm">
                        Add Your First KOL Post
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="space-y-8">
                    {Object.entries(kolsByTimeframe)
                      .map(([timeframe, posts]) => (
                        <div key={timeframe} className="space-y-4">
                          <div className="flex items-center gap-2">
                            <CalendarDays className="h-4 w-4 text-primary" />
                            <h3 className="text-lg font-semibold text-primary">{timeframe}</h3>
                            <span className="text-xs text-muted-foreground">
                              ({posts.length} {posts.length === 1 ? 'post' : 'posts'})
                            </span>
                          </div>

                          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
                            {posts.map((kol: KOL) => (
                              <Card key={kol.id} className="group overflow-hidden border hover:shadow-md transition-all duration-300 hover:border-primary/20">
                                <div className="h-2 bg-primary/30 w-full transition-all duration-300 group-hover:bg-primary"></div>
                                <CardHeader className="p-5 pb-2">
                                  <div className="flex items-start justify-between">
                                    <CardTitle className="text-base font-medium">
                                      {kol.account_handle ? `@${kol.account_handle}` : (kol.outlet || 'Unnamed')}
                                    </CardTitle>
                                    {kol.followers && (
                                      <div className="flex items-center">
                                        <Badge variant="outline" className="ml-2">
                                          {formatNumber(kol.followers)} Followers
                                        </Badge>
                                        <Popover>
                                          <PopoverTrigger asChild>
                                            <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
                                              <Info className="h-3.5 w-3.5 text-muted-foreground" />
                                            </Button>
                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-3 text-xs">
                                            Follower count at the time of posting
                                          </PopoverContent>
                                        </Popover>
                                      </div>
                                    )}
                                  </div>
                                </CardHeader>
                                <CardContent className="p-5 pt-0">
                                  <div className="flex flex-col space-y-3">
                                    {kol.post_date && (
                                      <div className="flex items-center text-sm text-muted-foreground">
                                        <CalendarDays className="h-3.5 w-3.5 mr-1" />
                                        <span>
                                          {kol.post_date}
                                        </span>
                                      </div>
                                    )}

                                    {kol.status && (
                                      <div className="flex items-center">
                                        <Badge variant="secondary" className={`text-xs ${kol.status === 'Done' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-800' : ''}`}>
                                          {kol.status}
                                        </Badge>
                                      </div>
                                    )}

                                    {kol.tweet_link && (
                                      <a
                                        href={kol.tweet_link}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="inline-flex items-center px-3 py-1.5 text-primary bg-primary/5 rounded-md hover:bg-primary/10 transition-colors"
                                      >
                                        {kol.tweet_link.includes('x.com') || kol.tweet_link.includes('twitter.com') ? (
                                          <><MessageCircle className="h-4 w-4 mr-1" /> View Tweet <ExternalLink className="h-3 w-3 ml-1.5" /></>
                                        ) : (
                                          <><LinkIcon className="h-4 w-4 mr-1" /> View Content <ExternalLink className="h-3 w-3 ml-1.5" /></>
                                        )}
                                      </a>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
}