import { SignIn } from '@clerk/nextjs';

export default function SignInPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/30 py-12">
      <div className="w-full max-w-md mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold mb-2">Social Media Tracker</h1>
          <p className="text-muted-foreground">Sign in to access your dashboard</p>
        </div>
        
        <SignIn 
          redirectUrl="/dashboard"
          appearance={{
            elements: {
              footer: "hidden",
              footerAction: "hidden",
              card: "shadow-lg rounded-lg",
              rootBox: "mx-auto"
            }
          }}
        />
      </div>
    </div>
  );
} 