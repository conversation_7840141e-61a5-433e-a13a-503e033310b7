'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { ArrowLeft, ExternalLink, Newspaper, Filter, FileText, RefreshCw, LogIn, AlertTriangle, CalendarDays, LinkIcon, Search } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { DashboardCard } from '@/components/dashboard-card';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';

interface PRCampaign {
  id: string;
  outlet: string;
  pr_type: string;
  link: string;
  created_at: string;
  updated_at?: string;
  user_id?: string;
  title?: string;
  reach?: string;
  status?: string;
  publish_date?: string;
  source?: string;
}

// Enum for sorting options
enum SortOption {
  DEFAULT = 'default',
  OUTLET_AZ = 'outlet_az',
  OUTLET_ZA = 'outlet_za',
  REACH_HIGH_TO_LOW = 'reach_high_to_low',
  REACH_LOW_TO_HIGH = 'reach_low_to_high',
  DATE_NEWEST = 'date_newest',
  DATE_OLDEST = 'date_oldest',
  STATUS = 'status'
}

export default function PRDashboard() {
  const [loading, setLoading] = useState(true);
  const [prCampaigns, setPrCampaigns] = useState<PRCampaign[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [incompleteDataWarning, setIncompleteDataWarning] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState<SortOption>(SortOption.DEFAULT);
  
  // Get authentication state
  const { userId, isLoaded, isSignedIn } = useAuth();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  const fetchPRCampaignsFromSheet = async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      
      // Only try to fetch if user is authenticated
      if (isSignedIn) {
        // Use the Google Sheets API route
        const response = await fetch('/api/pr-sheet-data');
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Process data but keep original values, just filter out completely empty records
        const processedData = data
          .filter((pr: PRCampaign) => pr.outlet || pr.pr_type || pr.link)
          .map((pr: PRCampaign) => ({
            ...pr
          }));
          
        // Only check for missing fields but don't add placeholders
        const hasIncompleteData = processedData.some(
          (pr: PRCampaign) => !pr.title && pr.status === 'Published'
        );
        
        setIncompleteDataWarning(hasIncompleteData);
        setPrCampaigns(processedData);
      }
    } catch (err) {
      console.error('Error fetching PR campaigns:', err);
      setError('Failed to load PR campaigns. Please try again later.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    if (isSignedIn) {
      fetchPRCampaignsFromSheet();
    }
  }, [isSignedIn]);

  // Extract unique PR types from the data to create tabs dynamically
  const prTypes = useMemo(() => {
    // Get all unique PR types, filtering out empty ones
    const uniqueTypes = Array.from(new Set(
      prCampaigns
        .filter(campaign => campaign.pr_type) // Only include campaigns with pr_type
        .map(campaign => campaign.pr_type)
    ));
    
    return uniqueTypes.length > 0 ? uniqueTypes : [];
  }, [prCampaigns]);
  
  // Filter campaigns based on active tab
  const filteredCampaigns = activeTab === 'all' 
    ? prCampaigns 
    : prCampaigns.filter(campaign => campaign.pr_type === activeTab);

  // Helper functions for sorting
  const sortPRCampaigns = (campaigns: PRCampaign[], sortBy: SortOption): PRCampaign[] => {
    const copiedCampaigns = [...campaigns];
    
    // Helper to parse reach/viewership count safely
    const parseReach = (reach?: string): number => {
      if (!reach) return 0;
      // Remove any non-numeric characters except decimal point
      const numericValue = reach.replace(/[^0-9.]/g, '');
      return parseFloat(numericValue) || 0;
    };
    
    // Helper to parse date
    const parseDate = (dateStr?: string): Date => {
      if (!dateStr) return new Date(0);
      
      try {
        // Parse date in format DD/MM/YYYY
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          // Format is Day/Month/Year
          const day = parseInt(parts[0], 10);
          const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
          const year = parseInt(parts[2], 10);
          
          const date = new Date(year, month, day);
          
          // Check if valid date
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
        
        // Fallback: try standard JS date parsing
        return new Date(dateStr);
      } catch (e) {
        console.error('Error parsing date:', dateStr, e);
        return new Date(0);
      }
    };
    
    switch (sortBy) {
      case SortOption.OUTLET_AZ:
        return copiedCampaigns.sort((a, b) => 
          (a.outlet || '').localeCompare(b.outlet || ''));
        
      case SortOption.OUTLET_ZA:
        return copiedCampaigns.sort((a, b) => 
          (b.outlet || '').localeCompare(a.outlet || ''));
      
      case SortOption.REACH_HIGH_TO_LOW:
        return copiedCampaigns.sort((a, b) => 
          parseReach(b.reach) - parseReach(a.reach));
      
      case SortOption.REACH_LOW_TO_HIGH:
        return copiedCampaigns.sort((a, b) => 
          parseReach(a.reach) - parseReach(b.reach));
      
      case SortOption.DATE_NEWEST:
        return copiedCampaigns.sort((a, b) => 
          parseDate(b.publish_date).getTime() - parseDate(a.publish_date).getTime());
      
      case SortOption.DATE_OLDEST:
        return copiedCampaigns.sort((a, b) => 
          parseDate(a.publish_date).getTime() - parseDate(b.publish_date).getTime());
      
      case SortOption.STATUS:
        return copiedCampaigns.sort((a, b) => 
          (a.status || '').localeCompare(b.status || ''));
      
      default:
        return copiedCampaigns;
    }
  };
  
  // Search function
  const searchPRCampaigns = (campaigns: PRCampaign[], term: string): PRCampaign[] => {
    if (!term.trim()) return campaigns;
    
    const lowercaseTerm = term.toLowerCase().trim();
    return campaigns.filter(campaign => 
      (campaign.outlet || '').toLowerCase().includes(lowercaseTerm) ||
      (campaign.title || '').toLowerCase().includes(lowercaseTerm) ||
      (campaign.pr_type || '').toLowerCase().includes(lowercaseTerm) ||
      (campaign.status || '').toLowerCase().includes(lowercaseTerm)
    );
  };
  
  // Apply sorting and filtering
  const processedCampaigns = useMemo(() => {
    const searchFiltered = searchPRCampaigns(filteredCampaigns, searchTerm);
    return sortPRCampaigns(searchFiltered, sortOption);
  }, [filteredCampaigns, searchTerm, sortOption]);

  const handleRefresh = () => {
    fetchPRCampaignsFromSheet();
  };
  
  // Show loading state while checking auth
  if (!isLoaded) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin w-10 h-10 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }
  
  // Show auth required message if not signed in
  if (!isSignedIn) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="max-w-md w-full">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">You need to sign in to access the PR dashboard.</p>
            <Link href="/sign-in">
              <Button className="w-full">
                <LogIn className="mr-2 h-4 w-4" /> Sign In
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // Show main dashboard when authenticated
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Link href="/dashboard" className="mr-2">
          <Button variant="outline" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">PR Dashboard</h1>
      </div>
      
      {loading && !refreshing ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin w-10 h-10 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-destructive">
              <AlertTriangle className="h-10 w-10 mx-auto mb-2" />
              <p>{error}</p>
              <Button onClick={handleRefresh} className="mt-4">Try Again</Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {incompleteDataWarning && (
            <Card className="mb-4 border-amber-300">
              <CardContent className="pt-6">
                <div className="flex items-center text-amber-600">
                  <AlertTriangle className="mr-2 h-5 w-5" />
                  <p>Some published campaigns are missing information. Please check the Google Sheet and fill in any blank fields.</p>
                </div>
              </CardContent>
            </Card>
          )}
          
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <Newspaper className="h-5 w-5 text-primary" />
              <h2 className="text-xl font-semibold">PR Campaigns</h2>
              <Badge variant="outline">{processedCampaigns.length}</Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Search campaigns..."
                  value={searchTerm}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                  className="w-[250px] mr-2"
                />
                <Search className="absolute right-6 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
              
              <Select value={sortOption} onValueChange={(value) => setSortOption(value as SortOption)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={SortOption.DEFAULT}>Default</SelectItem>
                  <SelectItem value={SortOption.OUTLET_AZ}>Outlet (A-Z)</SelectItem>
                  <SelectItem value={SortOption.OUTLET_ZA}>Outlet (Z-A)</SelectItem>
                  <SelectItem value={SortOption.REACH_HIGH_TO_LOW}>Reach (High to Low)</SelectItem>
                  <SelectItem value={SortOption.REACH_LOW_TO_HIGH}>Reach (Low to High)</SelectItem>
                  <SelectItem value={SortOption.DATE_NEWEST}>Date (Newest First)</SelectItem>
                  <SelectItem value={SortOption.DATE_OLDEST}>Date (Oldest First)</SelectItem>
                  <SelectItem value={SortOption.STATUS}>Status</SelectItem>
                </SelectContent>
              </Select>
              
              <Button onClick={handleRefresh} disabled={refreshing} variant="outline" size="sm">
                {refreshing ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
            </div>
          </div>
          
          {/* PR Type Tabs */}
          {prTypes.length > 0 && (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
              <TabsList className="bg-muted/50 p-1">
                <TabsTrigger value="all" className="rounded-sm">All</TabsTrigger>
                {prTypes.map(type => (
                  <TabsTrigger key={type} value={type} className="rounded-sm">{type}</TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          )}
          
          {/* Campaign Cards */}
          {refreshing ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="animate-pulse overflow-hidden border shadow-sm">
                  <div className="h-2 bg-primary/20 w-full"></div>
                  <CardHeader className="p-6">
                    <div className="h-6 w-2/3 bg-muted rounded"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-4 w-full bg-muted rounded mb-4"></div>
                    <div className="h-4 w-2/3 bg-muted rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : processedCampaigns.length === 0 ? (
            <Card className="border-dashed border-2">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Newspaper className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-center text-muted-foreground mb-4">No PR campaigns found with the current filters.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {processedCampaigns.map(campaign => (
                <Card key={campaign.id} className="overflow-hidden hover:shadow-md transition-all border border-border/40 hover:border-primary/20 group relative">
                  {/* Colored accent at top - style based on PR type */}
                  <div className={`h-1.5 w-full ${campaign.pr_type === 'Leak' ? 'bg-amber-500' : campaign.pr_type === 'Interview' ? 'bg-blue-500' : campaign.pr_type === 'Feature' ? 'bg-purple-500' : 'bg-primary/40'}`}></div>
                  
                  <CardHeader className="p-5 pb-2">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-base font-medium flex items-center">
                        {campaign.outlet}
                        {campaign.pr_type && (
                          <Badge variant="secondary" className="ml-2 text-[10px] px-1.5 py-0 rounded-sm bg-muted">
                            {campaign.pr_type}
                          </Badge>
                        )}
                      </CardTitle>
                      {campaign.reach && (
                        <Badge variant="outline" className="ml-2 font-medium text-xs bg-background">
                          {campaign.reach}
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-5 pt-2">
                    {/* Title/Content Section */}
                    {campaign.title && (
                      <p className="text-sm font-medium mb-3 line-clamp-2 group-hover:line-clamp-none transition-all duration-200">
                        {campaign.title.startsWith('http') ? 
                          <a 
                            href={campaign.title} 
                            target="_blank" 
                            rel="noopener noreferrer" 
                            className="inline-flex items-center text-primary hover:underline"
                          >
                            <LinkIcon className="h-3 w-3 mr-1 flex-shrink-0" /> Article <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
                          </a> : 
                          campaign.title
                        }
                      </p>
                    )}
                    
                    {/* Status and Date row */}
                    <div className="flex items-center justify-between mb-3">
                      {campaign.status && (
                        <Badge 
                          variant="outline"
                          className={`
                            text-xs rounded-sm px-2 
                            ${campaign.status === 'Published' 
                              ? 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800' 
                              : campaign.status === 'Pending'
                              ? 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800'
                              : 'bg-muted/50'
                            }`
                          }
                        >
                          {campaign.status}
                        </Badge>
                      )}
                      
                      {campaign.publish_date && (
                        <div className="flex items-center text-xs text-muted-foreground ml-auto">
                          <CalendarDays className="h-3 w-3 mr-1" />
                          <span>{campaign.publish_date}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Link Section */}
                    {campaign.link && campaign.link.startsWith('http') && (
                      <div className="mt-3 border-t pt-3 border-border/40">
                        <a 
                          href={campaign.link}
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="inline-flex items-center text-xs text-primary hover:underline"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" /> View Article
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
} 