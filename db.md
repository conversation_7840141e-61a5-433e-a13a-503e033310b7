-- Create the twitter_stats table
CREATE TABLE public.twitter_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    account_name TEXT NOT NULL,
    follower_count INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create the telegram_stats table
CREATE TABLE public.telegram_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    group_name TEXT NOT NULL,
    member_count INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.twitter_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.telegram_stats ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for twitter_stats

-- Allow service role to insert data
CREATE POLICY "Service can insert twitter stats" 
ON public.twitter_stats FOR INSERT 
TO authenticated 
USING (auth.role() = 'service_role');

-- Allow anyone to read twitter stats
CREATE POLICY "Anyone can view twitter stats" 
ON public.twitter_stats FOR SELECT 
TO anon, authenticated
USING (true);

-- Prevent updates and deletes
CREATE POLICY "No updates allowed on twitter stats" 
ON public.twitter_stats FOR UPDATE 
USING (false);

CREATE POLICY "No deletes allowed on twitter stats" 
ON public.twitter_stats FOR DELETE 
USING (false);

-- Create RLS policies for telegram_stats

-- Allow service role to insert data
CREATE POLICY "Service can insert telegram stats" 
ON public.telegram_stats FOR INSERT 
TO authenticated 
USING (auth.role() = 'service_role');

-- Allow anyone to read telegram stats
CREATE POLICY "Anyone can view telegram stats" 
ON public.telegram_stats FOR SELECT 
TO anon, authenticated
USING (true);

-- Prevent updates and deletes
CREATE POLICY "No updates allowed on telegram stats" 
ON public.telegram_stats FOR UPDATE 
USING (false);

CREATE POLICY "No deletes allowed on telegram stats" 
ON public.telegram_stats FOR DELETE 
USING (false);

-- Create indexes for better query performance
CREATE INDEX twitter_stats_timestamp_idx ON public.twitter_stats (timestamp);
CREATE INDEX twitter_stats_account_name_idx ON public.twitter_stats (account_name);
CREATE INDEX telegram_stats_timestamp_idx ON public.telegram_stats (timestamp);
CREATE INDEX telegram_stats_group_name_idx ON public.telegram_stats (group_name);

-- Grant privileges to authenticated users
GRANT SELECT ON public.twitter_stats TO anon, authenticated;
GRANT SELECT ON public.telegram_stats TO anon, authenticated;
GRANT INSERT ON public.twitter_stats TO authenticated;
GRANT INSERT ON public.telegram_stats TO authenticated;