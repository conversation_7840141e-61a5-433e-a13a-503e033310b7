# Social Media Stats Tracker

This project includes TypeScript scripts to track social media statistics and a Next.js dashboard to visualize the data.

## Features

- **Twitter Follower Tracker**: Daily tracking of follower counts for a specified Twitter account
- **Telegram Group Member Tracker**: Daily tracking of member counts for a specified Telegram group
- **Supabase Integration**: Stores all statistics in a Supabase database
- **Modern Dashboard**: Beautiful UI built with Next.js and shadcn/ui components
- **Real-time Graphs**: Interactive charts showing follower and member counts over time
- **Responsive Design**: Works on mobile, tablet, and desktop devices

## Prerequisites

- [Bun](https://bun.sh/) or Node.js
- [Supabase](https://supabase.com/) account with a project set up
- Twitter API credentials (v2 API)
- Telegram Bot token

## Installation

1. Clone this repository
2. Install dependencies:

```bash
bun install
```

3. Copy `.env.example` to `.env.local` and fill in your credentials:

```bash
cp .env.example .env.local
```

4. Copy `scripts/.env.example` to `scripts/.env` and fill in your credentials:

```bash
cp scripts/.env.example scripts/.env
```

5. Set up Supabase tables using the SQL in `supabase.sql`

## Usage

### Running the Dashboard

```bash
bun dev
```

Visit `http://localhost:3000` to view the dashboard.

### Running the Scripts

```bash
# Twitter stats tracker
bun scripts/twitter.ts

# Telegram stats tracker
bun scripts/telegram.ts
```

## Project Structure

```
/
  src/                   # Next.js app
    app/                 # Next.js app routes
      dashboard/         # Dashboard page
    components/          # React components
    lib/                 # Utilities and API clients
  scripts/               # Backend scripts
    twitter.ts           # Twitter follower count tracker
    telegram.ts          # Telegram member count tracker
    utils/               # Utility functions
  .env.example           # Example environment variables for frontend
  scripts/.env.example   # Example environment variables for scripts
```

## Dashboard Features

- **Analytics Cards**: Shows current follower/member counts with change indicators
- **Time Series Charts**: Visualizes data over time with interactive charts
- **Time Frame Selection**: View data for last 7, 30, or 90 days
- **Auto-refresh**: Data refreshes automatically every 5 minutes
- **Dark Mode Support**: Supports light and dark mode via system preferences

## Script Features

- **Rate Limiting**: Respects API rate limits to prevent throttling
- **Scheduled Execution**: Uses cron jobs for daily data collection
- **Error Handling**: Comprehensive error reporting and logging
- **Database Integration**: Stores all collected data in Supabase

## License

MIT
