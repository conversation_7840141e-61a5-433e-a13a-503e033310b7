# Social Media Stats Tracker

This project contains TypeScript scripts to track social media statistics and store them in a Supabase database.

## Features

- **Twitter Follower Tracker**: Daily tracking of follower counts for a specified Twitter account
- **Telegram Group Member Tracker**: Daily tracking of member counts for a specified Telegram group
- **Supabase Integration**: Stores all statistics in a Supabase database
- **Scheduling**: Uses cron scheduling for daily data collection
- **Error Handling**: Comprehensive error reporting and logging
- **Telegram Bot**: Includes a Telegram bot with commands for manually checking member counts

## Prerequisites

- [Bun](https://bun.sh/) or Node.js
- [Supabase](https://supabase.com/) account with a project set up
- Twitter API credentials (v2 API)
- Telegram Bot token

## Installation

1. Clone this repository
2. Install dependencies:

```bash
bun install
```

3. Copy `.env.example` to `.env` and fill in your credentials:

```bash
cp .env.example .env
```

4. Set up Supabase tables:

Create the following tables in your Supabase project:

**twitter_stats**:
- `id`: uuid (primary key, auto-generated)
- `timestamp`: timestamptz
- `account_name`: text
- `follower_count`: integer

**telegram_stats**:
- `id`: uuid (primary key, auto-generated)
- `timestamp`: timestamptz
- `group_name`: text
- `member_count`: integer

## Usage

### Twitter Stats Tracker

Run the Twitter stats tracker:

```bash
bun scripts/twitter.ts
```

This will:
1. Immediately check the follower count for the configured Twitter account
2. Save the data to the Supabase database
3. Schedule daily checks according to the cron schedule in the `.env` file

### Telegram Stats Tracker

Run the Telegram stats tracker:

```bash
bun scripts/telegram.ts
```

This will:
1. Immediately check the member count for the configured Telegram group
2. Save the data to the Supabase database
3. Schedule daily checks according to the cron schedule in the `.env` file
4. Start a Telegram bot that responds to the `/membercount` command (for authorized users)

## Environment Variables

See `.env.example` for all required environment variables.

## Telegram Bot Commands

- `/membercount` - Get the current member count for the configured Telegram group (restricted to users in the `TELEGRAM_ADMIN_USER_IDS` list)

## Project Structure

```
/scripts
  /twitter.ts         # Twitter follower count tracker
  /telegram.ts        # Telegram member count tracker
  /utils
    /logger.ts        # Logging utility
    /database.ts      # Supabase database utility
    /scheduler.ts     # Scheduling utility
  /.env.example       # Example environment variables
  /README.md          # This file
```

## Logs

Logs are stored in the `logs` directory:
- `logs/combined.log`: All logs
- `logs/error.log`: Error logs only

## License

MIT 