import { TwitterApi } from 'twitter-api-v2';
import dotenv from 'dotenv';
import logger from './utils/logger';
import { saveTwitterStats, initializeDatabase } from './utils/database';
import { runAndScheduleDaily } from './utils/scheduler';

// Load environment variables
dotenv.config();

// Validate required environment variables
const requiredEnvVars = [
  'TWITTER_API_KEY', 
  'TWITTER_API_SECRET', 
  'TWITTER_ACCESS_TOKEN', 
  'TWITTER_ACCESS_SECRET',
  'TWITTER_ACCOUNT_USERNAME'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    logger.error(`Missing required environment variable: ${envVar}`);
    process.exit(1);
  }
}

// Create Twitter client
const twitterClient = new TwitterApi({
  appKey: process.env.TWITTER_API_KEY!,
  appSecret: process.env.TWITTER_API_SECRET!,
  accessToken: process.env.TWITTER_ACCESS_TOKEN!,
  accessSecret: process.env.TWITTER_ACCESS_SECRET!,
});

// Rate limiter - Twitter API v2 has a rate limit of 300 requests per 15-minute window for GET endpoints
// We'll be conservative with a single request per hour for our daily stats tracking
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds
let lastRequestTime = 0;

// The target Twitter account to track
const targetAccount = process.env.TWITTER_ACCOUNT_USERNAME!;

/**
 * Enforces rate limiting for Twitter API calls
 */
async function enforceRateLimit(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_WINDOW) {
    const waitTime = RATE_LIMIT_WINDOW - timeSinceLastRequest;
    logger.info(`Rate limit: Waiting ${waitTime}ms before making the next Twitter API request`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  
  lastRequestTime = Date.now();
}

/**
 * Fetch follower count for the target Twitter account
 */
async function fetchFollowerCount(): Promise<number> {
  try {
    logger.info(`Fetching follower count for Twitter account: ${targetAccount}`);
    
    // Enforce rate limiting
    await enforceRateLimit();
    
    // Get user by username
    const user = await twitterClient.v2.userByUsername(targetAccount, {
      'user.fields': ['public_metrics']
    });
    
    if (!user.data) {
      throw new Error(`User not found: ${targetAccount}`);
    }
    
    // Extract follower count
    const followerCount = user.data.public_metrics?.followers_count;
    
    if (followerCount === undefined) {
      throw new Error('Failed to retrieve follower count');
    }
    
    logger.info(`Successfully fetched follower count for ${targetAccount}: ${followerCount}`);
    return followerCount;
  } catch (error) {
    // Check if the error is related to rate limiting
    if (error instanceof Error && error.message.includes('Rate limit')) {
      logger.warn(`Twitter API rate limit exceeded. Waiting before retrying.`);
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_WINDOW));
      return fetchFollowerCount(); // Retry after waiting
    }
    
    logger.error(`Error fetching follower count: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

/**
 * Main function to fetch Twitter stats and save to database
 */
async function trackTwitterStats(): Promise<void> {
  try {
    // Fetch follower count
    const followerCount = await fetchFollowerCount();
    
    // Save to database
    await saveTwitterStats(targetAccount, followerCount);
    
    logger.info(`Successfully tracked Twitter stats for ${targetAccount}`);
  } catch (error) {
    logger.error(`Failed to track Twitter stats: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

/**
 * Initialize and run the Twitter tracking script
 */
async function main() {
  try {
    logger.info('Starting Twitter statistics tracking');
    
    // Initialize database
    await initializeDatabase();
    
    // Set the cron schedule (default: midnight UTC)
    const cronSchedule = process.env.TWITTER_CRON_SCHEDULE || '0 0 * * *';
    
    // Run immediately and schedule for daily execution
    await runAndScheduleDaily('Twitter Stats Tracker', trackTwitterStats, cronSchedule);
    
    logger.info(`Twitter stats tracker scheduled with cron: ${cronSchedule}`);
  } catch (error) {
    logger.error(`Twitter stats tracker initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
}

// Run the script if executed directly
if (require.main === module) {
  main().catch(error => {
    logger.error(`Unhandled error in main function: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  });
}

export { trackTwitterStats, fetchFollowerCount }; 