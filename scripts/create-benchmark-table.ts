import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('Missing Supabase credentials in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Create benchmark table and insert initial data
async function setupBenchmarkTable() {
  console.log('Setting up benchmark table...');
  
  try {
    // Create the benchmarks table (this is SQL, which you would normally do in the Supabase dashboard)
    // Here we're just inserting the initial benchmark data
    
    // First check if the table already has data
    const { data: existingData, error: checkError } = await supabase
      .from('benchmarks')
      .select('*');
      
    if (checkError) {
      // Table likely doesn't exist yet, so we'll need to create it in the Supabase dashboard
      console.warn('Unable to check for existing benchmarks. Make sure the "benchmarks" table exists in Supabase with columns:');
      console.warn('- id (uuid, primary key)');
      console.warn('- platform (text, e.g., "twitter" or "telegram")');
      console.warn('- target_date (timestamp)');
      console.warn('- target_value (integer)');
      console.warn('- created_at (timestamp with default now())');
      console.error(checkError);
      return;
    }
    
    if (existingData && existingData.length > 0) {
      console.log('Benchmark data already exists. Skipping insertion.');
      console.log('Current benchmarks:');
      console.table(existingData);
      return;
    }
    
    // Create example benchmark targets for the next 6 months
    const today = new Date();
    const benchmarks = [];
    
    // Current baseline estimates
    const currentTwitterFollowers = 37000; // Base estimate
    const currentTelegramMembers = 33000;  // Base estimate
    
    // Twitter monthly growth targets (increasing by ~8-10% each month)
    for (let i = 1; i <= 6; i++) {
      const targetDate = new Date(today);
      targetDate.setMonth(today.getMonth() + i);
      
      // Twitter - add to array with compounding growth
      const twitterTarget = Math.round(currentTwitterFollowers * Math.pow(1.085, i));
      benchmarks.push({
        platform: 'twitter',
        target_date: targetDate.toISOString(),
        target_value: twitterTarget
      });
      
      // Telegram - add to array with compounding growth
      const telegramTarget = Math.round(currentTelegramMembers * Math.pow(1.075, i));
      benchmarks.push({
        platform: 'telegram',
        target_date: targetDate.toISOString(),
        target_value: telegramTarget
      });
    }
    
    // Insert benchmark data
    const { error: insertError } = await supabase
      .from('benchmarks')
      .insert(benchmarks);
      
    if (insertError) {
      console.error('Error inserting benchmark data:', insertError);
      return;
    }
    
    console.log('Benchmark data inserted successfully!');
    console.log('Created the following benchmarks:');
    
    // Fetch and display the inserted data
    const { data: resultData } = await supabase
      .from('benchmarks')
      .select('*')
      .order('target_date', { ascending: true });
      
    console.table(resultData);
    
  } catch (error) {
    console.error('Error setting up benchmark table:', error);
  }
}

// Run the setup
setupBenchmarkTable()
  .then(() => {
    console.log('Setup complete!');
    process.exit(0);
  })
  .catch(err => {
    console.error('Setup failed:', err);
    process.exit(1);
  }); 