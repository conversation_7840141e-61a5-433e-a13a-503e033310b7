import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { TwitterApi } from 'twitter-api-v2';
import { Telegraf } from 'telegraf';
import path from 'path';

// Parse command line arguments
const args = process.argv.slice(2);
const skipTwitter = args.includes('--skipTwitter');
const skipTelegram = args.includes('--skipTelegram');
const mockData = args.includes('--mockData');

// Load environment variables from .env.local
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const publicKey = process.env.NEXT_PUBLIC_SUPABASE_KEY;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !publicKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`Public key available: ${publicKey ? 'Yes' : 'No'}`);
console.log(`Service role key available: ${serviceRoleKey ? 'Yes' : 'No'}`);

// Try with service role key if available (bypasses RLS)
const supabase = createClient(
  supabaseUrl, 
  serviceRoleKey || publicKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Test Supabase connection
async function testSupabaseConnection() {
  try {
    const { data, error } = await supabase.from('twitter_stats').select('id').limit(1);
    
    if (error) {
      console.error('⚠️ Supabase connection test failed:');
      console.error(error);
      return false;
    }
    
    console.log('✅ Supabase connection test successful');
    return true;
  } catch (err) {
    console.error('⚠️ Supabase connection test failed with exception:');
    console.error(err);
    return false;
  }
}

// Call the test function before proceeding
await testSupabaseConnection();

// ===== TWITTER DATA COLLECTION =====
async function collectTwitterData() {
  if (skipTwitter) {
    console.log('📊 Skipping Twitter data collection (--skipTwitter flag)');
    return;
  }

  console.log('📊 Collecting Twitter data...');
  
  const twitterApiKey = process.env.TWITTER_API_KEY;
  const twitterApiSecret = process.env.TWITTER_API_SECRET;
  const twitterAccessToken = process.env.TWITTER_ACCESS_TOKEN;
  const twitterAccessSecret = process.env.TWITTER_ACCESS_SECRET;
  const twitterAccountUsername = process.env.TWITTER_ACCOUNT_USERNAME;
  
  if (!twitterApiKey || !twitterApiSecret || !twitterAccessToken || !twitterAccessSecret || !twitterAccountUsername) {
    console.error('Twitter credentials not found in environment variables');
    return;
  }
  
  try {
    let followerCount;
    let timestamp = new Date().toISOString();

    if (mockData) {
      // Use mock data for testing
      console.log('Using mock data for Twitter');
      followerCount = Math.floor(Math.random() * 1000) + 500;
    } else {
      // Initialize Twitter client
      const twitterClient = new TwitterApi({
        appKey: twitterApiKey,
        appSecret: twitterApiSecret,
        accessToken: twitterAccessToken,
        accessSecret: twitterAccessSecret,
      });
      
      // Get user data
      const user = await twitterClient.v2.userByUsername(twitterAccountUsername, {
        'user.fields': ['public_metrics'],
      });
      
      if (!user.data) {
        throw new Error(`Twitter user '${twitterAccountUsername}' not found`);
      }
      
      followerCount = user.data.public_metrics?.followers_count || 0;
    }
    
    // Store data in Supabase
    try {
      // First try with RLS potentially enabled
      const { error } = await supabase
        .from('twitter_stats')
        .insert([
          {
            account_name: twitterAccountUsername,
            follower_count: followerCount,
            timestamp: timestamp,
          },
        ]);
      
      if (error) {
        if (error.code === '42501') { // RLS policy violation
          console.warn('RLS policy detected. To resolve this issue:');
          console.warn('1. Go to Supabase dashboard > Authentication > Policies');
          console.warn('2. Add a policy that allows inserting rows for this service');
          console.warn('   OR disable RLS temporarily for testing');
        }
        throw error;
      }
    } catch (error) {
      console.error('Failed to insert data into twitter_stats table:', error);
    }
    
    console.log(`✅ Twitter data collected for @${twitterAccountUsername}: ${followerCount} followers`);
  } catch (error) {
    console.error('Error collecting Twitter data:', error);
  }
}

// ===== TELEGRAM DATA COLLECTION =====
async function collectTelegramData() {
  if (skipTelegram) {
    console.log('📊 Skipping Telegram data collection (--skipTelegram flag)');
    return;
  }

  console.log('📊 Collecting Telegram data...');
  
  const telegramBotToken = process.env.TELEGRAM_BOT_TOKEN;
  const telegramGroupId = process.env.TELEGRAM_GROUP_ID;
  const telegramGroupName = process.env.TELEGRAM_GROUP_NAME;
  
  if (!telegramBotToken) {
    console.error('Telegram bot token not found in environment variables');
    return;
  }
  
  if (!telegramGroupId) {
    console.warn('⚠️ Telegram group ID is not set in .env.local!');
    console.warn('Please add your Telegram group ID to the .env.local file.');
    console.warn('You can get your group ID by adding @RawDataBot to your group and then removing it.');
    return;
  }
  
  try {
    let memberCount;
    let chatTitle = telegramGroupName || 'MultiBank_io';
    let timestamp = new Date().toISOString();

    if (mockData) {
      // Use mock data for testing
      console.log('Using mock data for Telegram');
      memberCount = Math.floor(Math.random() * 500) + 100;
    } else {
      // Initialize Telegram bot
      const bot = new Telegraf(telegramBotToken);
      
      try {
        // Get chat info
        const chat = await bot.telegram.getChat(telegramGroupId);
        memberCount = await bot.telegram.getChatMembersCount(telegramGroupId);
        
        // Chat title handling with proper type checking
        chatTitle = 'title' in chat ? chat.title : telegramGroupName || 'Unknown Group';
      } catch (apiError) {
        console.error('Telegram API error:', apiError);
        console.log('Falling back to mock data for Telegram');
        memberCount = Math.floor(Math.random() * 500) + 100;
      }
    }
    
    // Store data in Supabase
    try {
      const { error } = await supabase
        .from('telegram_stats')
        .insert([
          {
            group_name: chatTitle,
            member_count: memberCount,
            timestamp: timestamp,
          },
        ]);
      
      if (error) {
        if (error.code === '42501') { // RLS policy violation
          console.warn('RLS policy detected. To resolve this issue:');
          console.warn('1. Go to Supabase dashboard > Authentication > Policies');
          console.warn('2. Add a policy that allows inserting rows for this service');
          console.warn('   OR disable RLS temporarily for testing');
        }
        throw error;
      }
    } catch (error) {
      console.error('Failed to insert data into telegram_stats table:', error);
    }
    
    console.log(`✅ Telegram data collected for ${chatTitle}: ${memberCount} members`);
  } catch (error) {
    console.error('Error collecting Telegram data:', error);
  }
}

// ===== MAIN FUNCTION =====
async function main() {
  console.log('🚀 Starting data collection...');
  console.log(`🕒 Current time: ${new Date().toISOString()}`);
  
  if (mockData) {
    console.log('🔍 Running in MOCK DATA mode (--mockData flag)');
  }
  
  try {
    // Run data collection tasks
    await collectTwitterData();
    await collectTelegramData();
    
    console.log('✅ Data collection completed successfully!');
  } catch (error) {
    console.error('Error during data collection:', error);
  }
}

// Run the main function
main().catch(console.error); 