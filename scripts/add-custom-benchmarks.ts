import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('Missing Supabase credentials in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Define the benchmark data from the provided table with proper typing
type BenchmarkRow = [string, number, number | null, number | null];

const benchmarkData: BenchmarkRow[] = [
  // Format: [date, day, telegram_value, twitter_value]
  ["2025-03-24", 0, null, null],
  ["2025-03-25", 1, 1168, 3604],
  ["2025-03-26", 2, 2336, 7208],
  ["2025-03-27", 3, 3504, 10812],
  ["2025-03-28", 4, 4672, 14416],
  ["2025-03-29", 5, 5840, 18020],
  ["2025-03-30", 6, 7008, 21624],
  ["2025-03-31", 7, 8176, 25228],
  ["2025-04-01", 8, 9344, 28832],
  ["2025-04-02", 9, 10512, 32436],
  ["2025-04-03", 10, 11680, 36040],
  ["2025-04-04", 11, 12848, 39644],
  ["2025-04-05", 12, 14016, 43248],
  ["2025-04-06", 13, 15184, 46852],
  ["2025-04-07", 14, 16352, 50456],
  ["2025-04-08", 15, 17520, 54060],
  ["2025-04-09", 16, 18688, 57664],
  ["2025-04-10", 17, 19856, 61268],
  ["2025-04-11", 18, 21024, 64872],
  ["2025-04-12", 19, 22192, 68476],
  ["2025-04-13", 20, 23360, 72080],
  ["2025-04-14", 21, 24528, 75684],
  ["2025-04-15", 22, 25696, 79288],
  ["2025-04-16", 23, 26864, 82892],
  ["2025-04-17", 24, 28032, 86496],
  ["2025-04-18", 25, 29200, 90100],
  ["2025-04-19", 26, 30368, 93704],
  ["2025-04-20", 27, 31536, 97308],
  ["2025-04-21", 28, 32704, 100912],
  ["2025-04-22", 29, 33872, 104516],
  ["2025-04-23", 30, 35040, 108120],
  ["2025-04-24", 31, 36208, 111725],
  ["2025-04-25", 32, 37376, 115329],
  ["2025-04-26", 33, 38544, 118933],
  ["2025-04-27", 34, 39712, 122537],
  ["2025-04-28", 35, 40880, 126141],
  ["2025-04-29", 36, 42048, 129745],
  ["2025-04-30", 37, 43216, 133349],
  ["2025-05-01", 38, 44384, 136953],
  ["2025-05-02", 39, 45552, 140557],
  ["2025-05-03", 40, 46720, 144161],
  ["2025-05-04", 41, 47888, 147765],
  ["2025-05-05", 42, 49056, 151369],
  ["2025-05-06", 43, 50224, 154973],
  ["2025-05-07", 44, 51392, 158577],
  ["2025-05-08", 45, 52560, 162181],
  ["2025-05-09", 46, 53728, 165785],
  ["2025-05-10", 47, 54896, 169389],
  ["2025-05-11", 48, 56064, 172993],
  ["2025-05-12", 49, 57232, 176597],
  ["2025-05-13", 50, 58400, 180201],
  ["2025-05-14", 51, 59568, 183805],
  ["2025-05-15", 52, 60736, 187409],
  ["2025-05-16", 53, 61904, 191013],
  ["2025-05-17", 54, 63072, 194617],
  ["2025-05-18", 55, 64240, 198221],
  ["2025-05-19", 56, 65408, 201825],
  ["2025-05-20", 57, 66576, 205429],
  ["2025-05-21", 58, 67744, 209033],
  ["2025-05-22", 59, 68912, 212637],
  ["2025-05-23", 60, 70080, 216241],
  ["2025-05-24", 61, 71248, 219845]
];

async function uploadBenchmarks() {
  console.log('Clearing existing benchmark data...');

  // First, remove any existing records
  const { error: deleteError } = await supabase
    .from('benchmarks')
    .delete()
    .not('id', 'is', null);

  if (deleteError) {
    console.error('Error deleting existing benchmarks:', deleteError);
    return;
  }

  console.log('Inserting new benchmark data...');

  // Format data for insertion
  const benchmarkRecords = [];

  // Process the data to create records for both platforms
  for (const [date, day, telegramValue, twitterValue] of benchmarkData) {
    // Add Telegram benchmark if value exists
    if (telegramValue !== null) {
      benchmarkRecords.push({
        platform: 'telegram',
        target_date: new Date(date).toISOString(),
        target_value: telegramValue,
      });
    }

    // Add Twitter benchmark if value exists
    if (twitterValue !== null) {
      benchmarkRecords.push({
        platform: 'twitter',
        target_date: new Date(date).toISOString(),
        target_value: twitterValue,
      });
    }
  }

  // Insert all benchmark records
  const { error: insertError } = await supabase
    .from('benchmarks')
    .insert(benchmarkRecords);

  if (insertError) {
    console.error('Error inserting benchmark data:', insertError);
    return;
  }

  console.log(`Successfully added ${benchmarkRecords.length} benchmark records!`);

  // Fetch and display the inserted data
  const { data: resultData, error: fetchError } = await supabase
    .from('benchmarks')
    .select('*')
    .order('target_date', { ascending: true });

  if (fetchError) {
    console.error('Error fetching inserted benchmarks:', fetchError);
    return;
  }

  console.log(`Inserted ${resultData.length} benchmark records`);
  console.log('First few records:');
  console.table(resultData.slice(0, 5));
  console.log('Last few records:');
  console.table(resultData.slice(-5));
}

// Run the upload function
uploadBenchmarks()
  .then(() => {
    console.log('Benchmark upload complete!');
    process.exit(0);
  })
  .catch(err => {
    console.error('Benchmark upload failed:', err);
    process.exit(1);
  }); 