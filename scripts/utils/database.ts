import { createClient, SupabaseClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import logger from './logger';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

// Validate credentials
if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Missing Supabase credentials. Please check your .env file.';
  logger.error(errorMsg);
  throw new Error(errorMsg);
}

// Create Supabase client
const supabase: SupabaseClient = createClient(supabaseUrl, supabaseKey);

// Twitter stats functions
export async function saveTwitterStats(accountName: string, followerCount: number): Promise<void> {
  try {
    const { error } = await supabase
      .from('twitter_stats')
      .insert({
        timestamp: new Date().toISOString(),
        account_name: accountName,
        follower_count: followerCount
      });
    
    if (error) throw error;
    logger.info(`Saved Twitter stats for ${accountName}: ${followerCount} followers`);
  } catch (error) {
    logger.error(`Failed to save Twitter stats: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

// Telegram stats functions
export async function saveTelegramStats(groupName: string, memberCount: number): Promise<void> {
  try {
    const { error } = await supabase
      .from('telegram_stats')
      .insert({
        timestamp: new Date().toISOString(),
        group_name: groupName,
        member_count: memberCount
      });
    
    if (error) throw error;
    logger.info(`Saved Telegram stats for ${groupName}: ${memberCount} members`);
  } catch (error) {
    logger.error(`Failed to save Telegram stats: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

// Database initialization function
export async function initializeDatabase(): Promise<void> {
  try {
    // Check if tables exist, create if needed
    // Note: This is a simplified version. In a production environment,
    // you might want to use migrations or more sophisticated table management.
    logger.info('Checking database tables...');
    
    // This is a placeholder. Supabase doesn't directly support table creation via the JS client.
    // You would typically create tables via Supabase dashboard or migrations.
    // This function could be used to validate the existence of tables instead.
    
    const { data: twitterTable, error: twitterError } = await supabase
      .from('twitter_stats')
      .select('id')
      .limit(1);
    
    if (twitterError && twitterError.code === '42P01') {
      logger.warn('Twitter stats table not found. Please create it in the Supabase dashboard.');
      /* Table should have:
        id: uuid (primary key)
        timestamp: timestamptz
        account_name: text
        follower_count: integer
      */
    }
    
    const { data: telegramTable, error: telegramError } = await supabase
      .from('telegram_stats')
      .select('id')
      .limit(1);
    
    if (telegramError && telegramError.code === '42P01') {
      logger.warn('Telegram stats table not found. Please create it in the Supabase dashboard.');
      /* Table should have:
        id: uuid (primary key)
        timestamp: timestamptz
        group_name: text
        member_count: integer
      */
    }
    
    logger.info('Database check completed');
  } catch (error) {
    logger.error(`Database initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

export default supabase; 