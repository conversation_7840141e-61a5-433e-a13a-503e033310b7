import cron from 'node-cron';
import logger from './logger';

/**
 * Schedule a task to run daily at a specific time
 * @param taskName Name of the task for logging
 * @param cronExpression Cron expression (default: every day at midnight)
 * @param task Function to execute
 * @returns The scheduled cron job
 */
export function scheduleDaily(
  taskName: string,
  task: () => Promise<void>,
  cronExpression: string = '0 0 * * *'
): cron.ScheduledTask {
  logger.info(`Scheduling task "${taskName}" with cron expression: ${cronExpression}`);
  
  const scheduledTask = cron.schedule(cronExpression, async () => {
    logger.info(`Running scheduled task: ${taskName}`);
    try {
      await task();
      logger.info(`Task "${taskName}" completed successfully`);
    } catch (error) {
      logger.error(`Task "${taskName}" failed: ${error instanceof Error ? error.message : String(error)}`);
      // Here you could also implement a notification system (email, SMS, etc.)
    }
  }, {
    scheduled: true,
    timezone: 'UTC' // You can change this to your local timezone
  });
  
  return scheduledTask;
}

/**
 * Run a task immediately and then schedule it to run daily
 * @param taskName Name of the task for logging
 * @param cronExpression Cron expression (default: every day at midnight)
 * @param task Function to execute
 * @returns The scheduled cron job
 */
export async function runAndScheduleDaily(
  taskName: string,
  task: () => Promise<void>,
  cronExpression: string = '0 0 * * *'
): Promise<cron.ScheduledTask> {
  logger.info(`Running task "${taskName}" immediately before scheduling`);
  
  try {
    await task();
    logger.info(`Initial run of task "${taskName}" completed successfully`);
  } catch (error) {
    logger.error(`Initial run of task "${taskName}" failed: ${error instanceof Error ? error.message : String(error)}`);
  }
  
  return scheduleDaily(taskName, task, cronExpression);
}

export default {
  scheduleDaily,
  runAndScheduleDaily
}; 