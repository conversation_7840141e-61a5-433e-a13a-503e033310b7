import { Telegraf } from 'telegraf';
import dotenv from 'dotenv';
import logger from './utils/logger';
import { saveTelegramStats, initializeDatabase } from './utils/database';
import { runAndScheduleDaily } from './utils/scheduler';

// Load environment variables
dotenv.config();

// Validate required environment variables
const requiredEnvVars = [
  'TELEGRAM_BOT_TOKEN',
  'TELEGRAM_GROUP_ID', 
  'TELEGRAM_GROUP_NAME'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    logger.error(`Missing required environment variable: ${envVar}`);
    process.exit(1);
  }
}

// Create Telegram bot instance
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN!);

// The target Telegram group to track
const targetGroupId = process.env.TELEGRAM_GROUP_ID!;
const targetGroupName = process.env.TELEGRAM_GROUP_NAME!;

// Rate limiter - Telegram Bot API has a rate limit of 30 messages per second
// We'll be conservative with a single request per minute for our daily stats tracking
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute in milliseconds
let lastRequestTime = 0;

/**
 * Enforces rate limiting for Telegram API calls
 */
async function enforceRateLimit(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_WINDOW) {
    const waitTime = RATE_LIMIT_WINDOW - timeSinceLastRequest;
    logger.info(`Rate limit: Waiting ${waitTime}ms before making the next Telegram API request`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  
  lastRequestTime = Date.now();
}

/**
 * Get the member count of the target Telegram group
 */
async function getMemberCount(): Promise<number> {
  try {
    logger.info(`Getting member count for Telegram group: ${targetGroupName}`);
    
    // Enforce rate limiting
    await enforceRateLimit();
    
    // Get chat information from Telegram
    const chatInfo = await bot.telegram.getChat(targetGroupId);
    
    if (!chatInfo) {
      throw new Error(`Chat not found: ${targetGroupId}`);
    }
    
    // For supergroups and channels, we can get the member count directly
    if ('type' in chatInfo && (chatInfo.type === 'supergroup' || chatInfo.type === 'channel')) {
      // Enforce rate limiting again for the second API call
      await enforceRateLimit();
      
      const memberCount = await bot.telegram.getChatMembersCount(targetGroupId);
      logger.info(`Successfully fetched member count for ${targetGroupName}: ${memberCount}`);
      return memberCount;
    } else {
      throw new Error(`Chat type not supported: ${chatInfo.type}. Must be a supergroup or channel.`);
    }
  } catch (error) {
    // Check if the error is related to rate limiting (Telegram returns 429 Too Many Requests)
    if (error instanceof Error && (error.message.includes('429') || error.message.includes('Too Many Requests'))) {
      logger.warn(`Telegram API rate limit exceeded. Waiting before retrying.`);
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_WINDOW * 5)); // Wait longer for Telegram rate limits
      return getMemberCount(); // Retry after waiting
    }
    
    logger.error(`Error getting member count: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

/**
 * Main function to fetch Telegram stats and save to database
 */
async function trackTelegramStats(): Promise<void> {
  try {
    // Get member count
    const memberCount = await getMemberCount();
    
    // Save to database
    await saveTelegramStats(targetGroupName, memberCount);
    
    logger.info(`Successfully tracked Telegram stats for ${targetGroupName}`);
  } catch (error) {
    logger.error(`Failed to track Telegram stats: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

/**
 * Initialize and run the Telegram tracking script
 */
async function main() {
  try {
    logger.info('Starting Telegram statistics tracking');
    
    // Initialize database
    await initializeDatabase();
    
    // Set the cron schedule (default: midnight UTC)
    const cronSchedule = process.env.TELEGRAM_CRON_SCHEDULE || '0 0 * * *';
    
    // Run immediately and schedule for daily execution
    await runAndScheduleDaily('Telegram Stats Tracker', trackTelegramStats, cronSchedule);
    
    logger.info(`Telegram stats tracker scheduled with cron: ${cronSchedule}`);
    
    // Set up bot command for manual checking with rate limiting
    bot.command('membercount', async (ctx) => {
      try {
        // Only allow this command from authorized users
        const allowedUserIds = process.env.TELEGRAM_ADMIN_USER_IDS ? 
          process.env.TELEGRAM_ADMIN_USER_IDS.split(',').map(id => Number(id.trim())) : 
          [];
        
        if (allowedUserIds.length > 0 && !allowedUserIds.includes(ctx.from.id)) {
          return ctx.reply('You are not authorized to use this command.');
        }
        
        await ctx.reply('Checking member count...');
        
        // Apply rate limiting before running the tracking function
        await enforceRateLimit();
        
        // Run the tracking function
        await trackTelegramStats();
        
        // Apply rate limiting again before getting the updated count
        await enforceRateLimit();
        
        const memberCount = await getMemberCount();
        await ctx.reply(`Current member count for ${targetGroupName}: ${memberCount}`);
      } catch (error) {
        logger.error(`Error handling command: ${error instanceof Error ? error.message : String(error)}`);
        await ctx.reply('An error occurred while getting member count.');
      }
    });
    
    // Start the bot
    await bot.launch();
    logger.info('Telegram bot started');
    
    // Enable graceful stop
    process.once('SIGINT', () => bot.stop('SIGINT'));
    process.once('SIGTERM', () => bot.stop('SIGTERM'));
  } catch (error) {
    logger.error(`Telegram stats tracker initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
}

// Run the script if executed directly
if (require.main === module) {
  main().catch(error => {
    logger.error(`Unhandled error in main function: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  });
}

export { trackTelegramStats, getMemberCount }; 