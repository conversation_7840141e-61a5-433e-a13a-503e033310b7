# Data Visualization Logic for Dashboard Charts

This document details the data sources, processing steps, and visualization logic used for the Twitter and Telegram growth charts on the dashboard.

## 1. Data Sources (Supabase Tables)

The primary data comes from the following Supabase tables:

*   **`twitter_stats`**:
    *   `timestamp` (timestamptz): The date and time the data point was recorded.
    *   `follower_count` (int): The number of Twitter followers at that time.
    *   `account_name` (text): The Twitter account handle being tracked.
    *   *(Other columns like `id`, `user_id` exist but are less relevant for the chart itself).*
*   **`telegram_stats`**:
    *   `timestamp` (timestamptz): The date and time the data point was recorded.
    *   `member_count` (int): The number of Telegram members at that time.
    *   `group_name` (text): The Telegram group name being tracked.
    *   *(Other columns like `id`, `user_id` exist but are less relevant for the chart itself).*
*   **`benchmarks`**:
    *   `platform` ('twitter' | 'telegram'): Specifies which metric the benchmark applies to.
    *   `target_date` (date): The date by which the target should be met.
    *   `target_value` (int): The target follower/member count for that date.
    *   *(Other columns like `id`, `created_at` exist).*

Data is collected periodically by the script `scripts/collect-data.ts` (likely triggered via `src/app/api/cron/collect-data/route.ts`).

## 2. API Endpoint (`/api/stats`)

This Next.js API route (`src/app/api/stats/route.ts`) is responsible for fetching and preparing the data for the frontend.

*   **Authentication:** Requires user authentication via Clerk.
*   **Data Fetching:** Uses an admin Supabase client to fetch data, bypassing Row Level Security.
*   **Date Filtering:**
    *   Accepts `startDate` and `endDate` (ISO strings) or a `timeFrame` ('1d', '7d', '30d', '90d') as query parameters.
    *   Filters `twitter_stats` and `telegram_stats` based on the provided date range or timeframe using the `timestamp` column.
    *   Defaults to '1d' if no range is specified.
*   **Benchmark Fetching & Interpolation:**
    *   Fetches relevant `benchmarks` for Twitter and Telegram based on the requested date range (with an extended window: 1 month before start, 6 months after end, to provide context).
    *   **Interpolation:** If benchmark points are more than 14 days apart, it calculates and inserts *additional, linearly interpolated* benchmark data points at weekly intervals between the original points. This helps create smoother target lines on the charts.
*   **Response:** Returns a JSON object containing arrays:
    *   `twitter`: Filtered `twitter_stats` data, ordered descending by timestamp (latest first).
    *   `telegram`: Filtered `telegram_stats` data, ordered descending by timestamp.
    *   `twitter_benchmarks`: Filtered and interpolated benchmark data for Twitter, ordered ascending by `target_date`.
    *   `telegram_benchmarks`: Filtered and interpolated benchmark data for Telegram, ordered ascending by `target_date`.
    *   `pr`: All `pr_campaigns` data.
    *   `kols`: All `kols` data.

## 3. Dashboard Page (`src/app/dashboard/page.tsx`)

This page fetches data from the `/api/stats` endpoint and processes it further before passing it to the chart components.

*   **State Management:** Uses `useState` to store:
    *   Raw fetched data (`rawTwitterData`, `rawTelegramData`, `twitterBenchmarks`, `telegramBenchmarks`, etc.).
    *   Latest stats (`latestTwitterStats`, `latestTelegramStats`).
    *   The value at the *start* of the fetched period (`rawPreviousTwitterCount`, `rawPreviousTelegramCount`) - used for calculating overall % change.
    *   Date range controls (`sharedTimeRange`, `sharedDateRange`, `useSharedDateRange`).
    *   The state of the "Total" / "Growth" toggle (`showTotalCounts`).
    *   Loading and error states.
*   **Data Fetching (`fetchData`):**
    *   Called on initial load and periodically (every 5 mins) via `useEffect`.
    *   Constructs the API URL based on the selected date range/timeframe state.
    *   Fetches data from `/api/stats`.
    *   Updates state variables with the raw fetched data. Note: `rawPrevious...Count` is set to the *first* data point received from the API for the selected range.
*   **Data Processing (`processedChartData`):**
    *   Uses `useMemo` to recalculate chart-specific data whenever the raw data or the `showTotalCounts` switch changes.
    *   Takes the `rawTwitterData` or `rawTelegramData`.
    *   If `showTotalCounts` is `true`: Returns the raw data as is. `currentValue` is the latest value, `previousValue` is the value from the start of the period (`rawPrevious...Count`).
    *   If `showTotalCounts` is `false` (Growth view):
        *   Calculates a `baseline` (the value of the first data point in the raw data).
        *   Maps over the raw data, subtracting the `baseline` from each `value` (clamped at 0).
        *   Returns this new `growthData`. `currentValue` is the latest growth value, `previousValue` is set to 0 (as growth starts from 0 at the baseline).
*   **Rendering:**
    *   Passes the relevant processed data (`processedChartData.twitter.data`, `processedChartData.twitter.currentValue`, etc.) and raw benchmarks (`twitterBenchmarks`) to the `<EnhancedGrowthChart>` components.
    *   Passes the appropriate `valueLabel` ("Followers" vs. "Follower Growth") based on `showTotalCounts`.
    *   Passes color props (`color`, `positiveColor`, `negativeColor`, `secondaryColor`).

## 4. Chart Component (`src/components/ui/charts/enhanced-growth-chart.tsx`)

This component is responsible for the actual visualization.

*   **Props:** Receives processed `data`, `benchmarks`, `currentValue`, `previousValue`, `valueLabel`, colors, etc.
*   **Overall Trend Calculation:** Calculates `percentChange` based on the `currentValue` and `previousValue` props received from the dashboard (representing the change over the *entire selected period*).
*   **Line Color (`activeLineColor`):** Determines the *stroke* color for the main `<Area>` component based on the overall `percentChange` (positive -> `positiveColor`, negative -> `negativeColor`, zero -> `color`).
*   **Header Display:** Shows the `title`, `description`, `currentValue` (already processed for Total/Growth), and the overall `percentChange` badge. Also shows `nextBenchmark` info if available.
*   **Chart Data (`chartData` memo):** Formats the date from the received `data` prop for the X-axis (`MMM dd`) and maps benchmark values to corresponding dates for the 'Targets' view.
*   **Rendering Logic:**
    *   Uses `ChartContainer` and `ResponsiveContainer`.
    *   Conditionally renders either a `ComposedChart` (if `showBenchmarksView` is true) or an `AreaChart`.
    *   **Area Component (`<Area>`):**
        *   `dataKey`: Set to "value" (plots the processed data passed via the `data` prop).
        *   `stroke`: Set to `activeLineColor` (green/red/gray based on overall trend).
        *   `fill`: Currently set to a simple gradient based on `activeLineColor`. *(The attempt at daily blue/red fill was removed due to rendering issues).*
        *   `activeDot`: Configured to show a dot on hover. *(Currently not working)*.
        *   `isAnimationActive`: Set to `false`.
    *   **Line Component (`<Line>` in `ComposedChart`):** Plots the `target` data from `chartData` as a dashed line.
    *   **Tooltip (`<ChartTooltip>`):** Configured with `cursor={true}` and uses `ChartTooltipContent` to display data on hover. *(Currently not working)*.
    *   **Axes (`<XAxis>`, `<YAxis>`):** Configured for appearance and formatting.
*   **Footer Display:** Shows text indicating the overall trend direction, percentage, and date range description.

## 5. Date Controls Interaction

*   The `DateRangePicker` and the time frame `Select` dropdown (`Last 1d`, `Last 7d`, etc.) are located on the dashboard page.
*   Changing either control updates the `sharedDateRange` / `useSharedDateRange` or `sharedTimeRange` state variables on the dashboard.
*   The `useEffect` hook on the dashboard page has these state variables in its dependency array.
*   When these state variables change, the `useEffect` hook triggers the `fetchData` function.
*   `fetchData` sends the updated date range/timeframe to the `/api/stats` endpoint.
*   The API returns newly filtered data.
*   The dashboard updates its state with the new raw data.
*   The `processedChartData` memo recalculates based on the new raw data and `showTotalCounts`.
*   The `EnhancedGrowthChart` components re-render with the updated props.

## Summary of Potential Issues (Based on Debugging History)

*   **Hover Interactivity:** The primary unresolved issue is the lack of hover effects (`activeDot`, `ChartTooltip`) despite seemingly correct configuration. This might stem from CSS conflicts (overlays, z-index) or JavaScript event propagation issues on the dashboard page, preventing mouse events from reaching the Recharts SVG layer.
*   **Rendering Crashes:** Previous attempts to modify chart components (`SimpleAreaChart` introduction, layout changes in `EnhancedGrowthChart`) caused the entire page section to fail rendering, suggesting fragility either in the components or the data passed to them.
*   **(Removed) Daily Conditional Fill:** The attempt to color the area fill blue/red based on daily changes failed, likely due to the complexity of dynamic SVG gradient generation in Recharts. The current implementation uses a simple fill based on the overall trend color.

This detailed breakdown should clarify the intended data flow and identify areas where the current implementation might be failing, particularly concerning the hover interactivity.