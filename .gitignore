# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# logs
logs
*.log

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Build artifacts
dist/
.cache/

# Local Netlify folder
.netlify

# Serverless directories
.serverless/

# Supabase
.supabase/

# Temporary files
*.tmp
.temp/
tmp/

# clerk configuration (can include secrets)
/.clerk/
