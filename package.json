{"name": "multibank", "type": "module", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate-users": "bun scripts/user-migration.ts"}, "dependencies": {"@clerk/nextjs": "^6.12.9", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@supabase/supabase-js": "^2.49.1", "@types/node-cron": "^3.0.11", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "google-auth-library": "^9.15.1", "google-spreadsheet": "^4.1.4", "googleapis": "^148.0.0", "lucide-react": "^0.483.0", "next": "15.2.3", "next-themes": "^0.4.6", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-day-picker": "^9.6.3", "react-dom": "^19.0.0", "recharts": "^2.15.1", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "telegraf": "^4.16.3", "tw-animate-css": "^1.2.4", "twitter-api-v2": "^1.22.0", "winston": "^3.17.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.3", "@eslint/eslintrc": "^3"}}