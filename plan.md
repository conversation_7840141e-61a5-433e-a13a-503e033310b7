# Twitter and Telegram Data Collection Scripts

## Objectives
- [x] Create a plan for implementation
- [x] Set up required dependencies
- [x] Create a scripts directory
- [x] Implement `twitter.ts` script
- [x] Implement `telegram.ts` script
- [x] Set up Supabase database tables (design created, actual tables need to be created in Supabase dashboard)
- [x] Configure scheduling for daily execution
- [x] Create documentation

## Dependencies to Install
- [x] `@supabase/supabase-js` - For database interactions
- [x] `node-cron` - For scheduling
- [x] `dotenv` - For environment variables
- [x] `twitter-api-v2` - For Twitter API interactions
- [x] `telegraf` - For Telegram bot functionality
- [x] `winston` - For logging

## Implementation Details

### Directory Structure
```
/scripts
  /twitter.ts
  /telegram.ts
  /utils
    /logger.ts
    /database.ts
    /scheduler.ts
  /.env.example
```

### Twitter Script (`twitter.ts`)
- [x] Set up Twitter API client
- [x] Create function to fetch follower count
- [x] Implement database insertion logic
- [x] Add error handling
- [x] Configure daily scheduling

### Telegram Script (`telegram.ts`)
- [x] Set up Telegram bot
- [x] Create function to get member count
- [x] Implement database insertion logic
- [x] Add error handling
- [x] Configure daily scheduling

### Supabase Database
- [x] Design `twitter_stats` table with fields:
  - id (auto-generated)
  - timestamp
  - account_name
  - follower_count
- [x] Design `telegram_stats` table with fields:
  - id (auto-generated)
  - timestamp
  - group_name
  - member_count

### Scheduling
- [x] Implement cron job to run scripts once per day
- [x] Configure error notifications

### Error Handling
- [x] Implement robust error handling
- [x] Set up logging for both scripts
- [x] Create notification system for failures

## API Keys and Environment Variables
- [x] Document Twitter API credentials
- [x] Document Telegram Bot Token
- [x] Document Supabase URL and API Key
- [x] Document Target Twitter account name
- [x] Document Target Telegram group ID

## Running the Scripts

To use these scripts:

1. Copy `.env.example` to `.env` and fill in your actual credentials
2. Set up the required Supabase tables using the Supabase dashboard
3. Run the scripts with:
   ```
   bun scripts/twitter.ts
   bun scripts/telegram.ts
   ```

Each script will:
- Check immediately for data (follower count or member count)
- Store the results in the Supabase database
- Schedule a daily check (configurable via environment variables)

The Telegram script also provides a bot command `/membercount` for authorized users to manually check member counts.

# Clerk Authentication Integration Plan

## Objectives
- [x] Set up Clerk authentication
- [x] Protect all dashboard pages
- [x] Integrate Supabase with Clerk user IDs
- [x] Add user-specific data isolation

## Dependencies to Install
- [x] `@clerk/nextjs` - For authentication

## Implementation Steps

### 1. Setup and Installation
- [x] Install Clerk packages:
  ```bash
  bun add @clerk/nextjs
  ```
- [x] Create environment variables example file (.env.clerk.example)

### 2. Configure Clerk Provider
- [x] Wrap app with `ClerkProvider` in `src/app/layout.tsx`
- [x] Fix ClerkProvider placement in layout (placed inside html/body tags)

### 3. Create Authentication Pages
- [x] Create sign-in page (`src/app/sign-in/page.tsx`)
- [x] Create sign-up page (`src/app/sign-up/page.tsx`)

### 4. Create Middleware for Authentication Protection
- [x] Create `middleware.ts` in the root directory to protect routes

### 5. Add User Session Components
- [x] Create a user button component (`src/components/user-button.tsx`)
- [x] Add the UserButton to the SiteHeader

### 6. Supabase Integration with Clerk
- [x] Create database migration SQL file (`clerk_migration.sql`)
- [x] Fixed SQL type issues (added ::text casts to UUID comparisons)
- [x] Create updated API functions with user context (src/lib/supabase.ts)
- [x] Created client-side Supabase hooks (src/lib/supabase-client.ts)

### 7. Testing
- [x] Test public route access - Created auth test page at /auth-test
- [x] Test authentication flow (sign up, sign in, sign out) - Added UserButton with sign out functionality
- [x] Test protected routes and redirects - Added middleware configuration
- [x] Test user-specific data isolation - Implemented data filtering based on user ID

### 8. Deployment
- [ ] Add Clerk environment variables to production environment
- [ ] Update Supabase production database with new schemas and policies

## Next Steps

1. Apply the updated SQL migration in the Supabase dashboard
2. Add Clerk API keys to `.env.local` using the example in `.env.clerk.example`
3. Run the application and test the authentication flow
4. Update any client components to use client-side Supabase hooks from supabase-client.ts
5. [x] Assign user IDs to existing data in the database - Created script `scripts/user-migration.ts`
6. [x] Create comprehensive documentation for Clerk setup - Created `CLERK_SETUP.md`
7. [x] Add settings page for user profile management - Created at `/settings`

# Project Plan

- [X] Investigate why "Verified Emails", "Verified Numbers", and "KYC Completed" show 0.
  - [X] Verify column names from screenshot.
  - [X] Update `parseVerification` to handle numbers and common true/false strings.
  - [X] Add server-side logging to inspect raw values from sheet and parsed values for the problematic columns.
  - [X] Update code to use column names from the latest screenshot: `Email Verified #`, `Phone Verified #`, `KYC Complete`.
  - [X] Adjust KYC column name to `KYC Complete #` based on user feedback.
  - [ ] Update KYC column name to `KYC Completed #` based on the newest screenshot.
  - [ ] Analyze logs if issues *still* persist.
  - [ ] Implement fix based on log analysis.
- [ ] Confirm all dashboard metrics are correct. 